# 会员分期支付功能实现说明

## 功能概述

实现会员订单分期支付功能，支持2期分期：
- **首期**：用户必须主动支付
- **二期**：支持两种支付方式（用户主动支付 或 通过借款单自动划扣支付）

## 技术实现详情

### 1. 数据库表结构修改

#### 1.1 plus_order_info 表新增字段
```sql
ALTER TABLE `plus_order_info` 
ADD COLUMN `first_pay_amount` DECIMAL(10,2) NULL COMMENT '首付金额（分期支付场景使用）' 
AFTER `pay_type`;
```

#### 1.2 支付方式枚举扩展
- 已存在枚举值：`PAY_FIRST_PERIOD(5, "首期支付", Boolean.FALSE)`
- 支付方式注释更新为：`1-全款支付2-划扣3-后付款4-全款划扣5-首期支付`

### 2. 代码实现修改

#### 2.1 实体类修改
**文件**：`PlusOrderInfoPo.java`、`PlusOrderEntity.java`
- 新增 `firstPayAmount` 字段
- 新增对应的 getter/setter 方法

#### 2.2 Mapper XML 修改
**文件**：`PlusOrderInfoMapper.xml`
- 在所有字段列表中添加 `first_pay_amount`
- 在插入语句中添加 `first_pay_amount` 字段和对应的参数绑定

#### 2.3 业务逻辑实现
**文件**：`PlusOrderModelImpl.java`
- 在订单创建流程中添加首付金额计算逻辑
- 计算公式：`firstPayAmount = Math.floor(会员方案原价 * 折扣比例)`
- 仅在支付方式为 `PAY_FIRST_PERIOD` 时计算首付金额

#### 2.4 订单服务调用修改
**文件**：`OrderExternalRepositoryAcl.java`
- 在 `emberOrderCommit` 方法中添加分期支付处理逻辑
- 当支付方式为 `PAY_FIRST_PERIOD` 时：
  - 设置 `orderType` 为 `1205`（分期支付订单类型）
  - 设置 `downpaymentAmount` 为计算得出的首付金额

#### 2.5 订单保存逻辑修改
**文件**：`OrderRepositoryImpl.java`
- 在 `saveOrderInfo` 方法中添加首付金额的保存逻辑
- 确保首付金额正确写入数据库

### 3. 核心业务流程

#### 3.1 首付金额计算流程
```java
// 仅在分期支付场景下计算
if (PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue().equals(payType)) {
    BigDecimal originalPrice = plusProgramEntity.getProgramPrice();
    BigDecimal discountedPrice = discountRate != null ? 
        originalPrice.multiply(discountRate) : originalPrice;
    firstPayAmount = new BigDecimal(Math.floor(discountedPrice.doubleValue()));
}
```

#### 3.2 订单服务调用流程
```java
// 分期支付订单类型设置
if (PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.equals(payTypeEnum)) {
    memberVipVO.setOperateType("1205");  // 固定值，表示分期支付订单类型
    if (order.getFirstPayAmount() != null) {
        memberVipVO.setDownpaymentAmount(order.getFirstPayAmount());
    }
}
```

### 4. 关键技术点

#### 4.1 首付金额精度处理
- 使用 `Math.floor()` 进行向下取整
- 确保金额精度为小数点后2位

#### 4.2 支付方式判断
- 通过 `PlusOrderPayTypeEnum.PAY_FIRST_PERIOD` 枚举值判断是否为分期支付
- 在多个关键节点进行支付方式检查

#### 4.3 数据一致性保证
- 在订单创建、保存、服务调用等多个环节确保首付金额的正确传递
- 通过事务控制保证数据的一致性

### 5. 兼容性考虑

#### 5.1 向后兼容
- 新增字段设置为可空，不影响现有订单
- 现有支付流程保持不变
- 仅在分期支付场景下启用新逻辑

#### 5.2 与现有收银台支付流程兼容
- 分期支付的首期支付仍然通过现有的收银台流程处理
- 支付参数生成、回调处理等逻辑保持一致

### 6. 测试要点

#### 6.1 功能测试
- 验证首付金额计算是否正确
- 验证订单服务调用参数是否正确传递
- 验证数据库字段是否正确保存

#### 6.2 兼容性测试
- 验证现有支付方式不受影响
- 验证数据库表结构变更不影响现有功能

#### 6.3 边界条件测试
- 测试折扣率为null的情况
- 测试首付金额为0的情况
- 测试精度处理的准确性

### 7. 部署注意事项

#### 7.1 数据库变更
- 先在测试环境执行SQL脚本
- 确认字段添加成功后再在生产环境执行
- 建议在业务低峰期执行

#### 7.2 代码部署
- 建议采用灰度发布策略
- 先部署到测试环境进行充分测试
- 监控相关日志和指标

### 8. 后续扩展

#### 8.1 二期支付处理
- 当前实现主要针对首期支付
- 二期支付的自动划扣逻辑需要后续实现
- 需要考虑与借款单系统的集成

#### 8.2 分期支付状态管理
- 可能需要增加分期支付的状态跟踪
- 考虑增加分期支付的查询和管理功能

## 总结

本次实现完成了会员分期支付功能的基础架构，包括数据库表结构修改、实体类扩展、业务逻辑实现和订单服务调用适配。实现过程中充分考虑了向后兼容性和与现有系统的集成，为后续的功能扩展奠定了基础。
