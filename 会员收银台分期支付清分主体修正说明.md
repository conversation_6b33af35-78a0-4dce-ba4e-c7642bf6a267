# 会员收银台分期支付清分主体修正说明

## 问题描述

在原有的会员收银台分期支付功能实现中，第二期支付的清分主体逻辑存在缺陷：
- 第二期支付没有正确复用第一期支付的清分主体记录
- 缺少从 `plus_order_separate_item` 表查询第一期清分主体详细信息的逻辑
- 第二期支付的清分主体配置可能与第一期不一致

## 修正方案

### 1. 新增数据查询接口

#### 1.1 Repository 接口扩展
**文件**：`IPlusOrderSeparateRepository.java`
```java
/**
 * 根据订单号和支付动作查询分账明细
 */
List<PlusOrderSeparateItemEntity> getSeparateItemsByOrderSnAndPayAction(String orderSn, 
        Integer orderPayAction);
```

#### 1.2 Mapper 接口扩展
**文件**：`IPlusOrderSeparateItemMapper.java`
```java
/**
 * 根据订单号和支付动作查询分账明细
 */
List<PlusOrderSeparateItemPo> getSeparateItemsByOrderSnAndPayAction(
        @Param("orderSn") String orderSn,
        @Param("orderPayAction") Integer orderPayAction);
```

#### 1.3 SQL 查询实现
**文件**：`PlusOrderSeparateItemMapper.xml`
```xml
<select id="getSeparateItemsByOrderSnAndPayAction"
        resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparateItemPo">
    SELECT si.*
    FROM plus_order_separate s, plus_order_separate_item si
    WHERE s.order_sn = si.order_sn
      AND s.apply_serial_no = si.apply_serial_no
      AND s.order_sn = #{orderSn}
      AND s.order_pay_action = #{orderPayAction}
    ORDER BY si.create_time desc
</select>
```

### 2. 核心业务逻辑修正

#### 2.1 第二期支付分账信息获取修正
**文件**：`PlusOrderApplicationImpl.java`

**修正前的问题**：
- 只查询了第一期的分账主记录
- 没有获取清分主体的详细配置信息

**修正后的逻辑**：
```java
private PlusOrderDeductResEntity getSecondPeriodSeparateInfo(PlusOrderEntity plusOrderEntity, 
        PlusDeductEvent deductPlan) {
    String orderSn = plusOrderEntity.getOrderSn();
    
    // 查询第一期支付的分账记录
    List<PlusOrderSeparateEntity> firstPeriodSeparates = separateModel.getPlusOrderSeparate(orderSn)
            .stream()
            .filter(s -> OrderPayActionEnum.PAY_ACTION_3.getCode().equals(s.getOrderPayAction()))
            .collect(Collectors.toList());
    
    if (firstPeriodSeparates.isEmpty()) {
        throw new PlusAbyssException("未找到第一期支付的分账记录，无法进行第二期支付");
    }
    
    // 获取第一期的分账信息作为模板
    PlusOrderSeparateEntity firstPeriodSeparate = firstPeriodSeparates.get(0);
    
    // 【新增】查询第一期支付的清分主体明细记录
    List<PlusOrderSeparateItemEntity> firstPeriodItems = separateRepository.getSeparateItemsByOrderSnAndPayAction(
            orderSn, OrderPayActionEnum.PAY_ACTION_3.getCode());
    
    if (firstPeriodItems.isEmpty()) {
        throw new PlusAbyssException("未找到第一期支付的清分主体记录，无法进行第二期支付");
    }
    
    // 计算第二期支付金额
    BigDecimal totalAmount = plusOrderEntity.getOrderAmount();
    BigDecimal firstPayAmount = plusOrderEntity.getFirstPayAmount();
    BigDecimal secondPayAmount = totalAmount.subtract(firstPayAmount);
    
    if (secondPayAmount.compareTo(BigDecimal.ZERO) <= 0) {
        throw new PlusAbyssException("第二期支付金额计算错误，金额不能小于等于0");
    }
    
    // 【修正】传递第一期的清分主体明细记录
    return createSecondPeriodSeparateInfo(plusOrderEntity, deductPlan, firstPeriodSeparate, 
            firstPeriodItems, secondPayAmount);
}
```

#### 2.2 第二期分账信息创建逻辑重构
**修正前的问题**：
- 通过调用 `separateModel.getPlusOrderSeparateInfo()` 重新生成分账信息
- 只复用了分流主体信息，没有复用清分主体配置

**修正后的逻辑**：
```java
private PlusOrderDeductResEntity createSecondPeriodSeparateInfo(PlusOrderEntity plusOrderEntity,
        PlusDeductEvent deductPlan, PlusOrderSeparateEntity firstPeriodTemplate, 
        List<PlusOrderSeparateItemEntity> firstPeriodItems, BigDecimal secondPayAmount) {

    // 创建第二期分账主记录
    PlusOrderSeparateEntity secondPeriodSeparate = new PlusOrderSeparateEntity();
    secondPeriodSeparate.setOrderSn(plusOrderEntity.getOrderSn());
    secondPeriodSeparate.setUserId(plusOrderEntity.getUserId());
    secondPeriodSeparate.setApplySerialNo(getApplySerialNo(deductPlan.getPayAction(), plusOrderEntity.getOrderSn()));
    secondPeriodSeparate.setOrderPayAction(OrderPayActionEnum.PAY_ACTION_4.getCode());
    
    // 【核心修正】复用第一期的分流主体信息
    secondPeriodSeparate.setShuntSupplierId(firstPeriodTemplate.getShuntSupplierId());
    secondPeriodSeparate.setSeparateEnableState(firstPeriodTemplate.getSeparateEnableState());
    secondPeriodSeparate.setSettleEnableState(firstPeriodTemplate.getSettleEnableState());
    secondPeriodSeparate.setBusinessScene(firstPeriodTemplate.getBusinessScene());
    secondPeriodSeparate.setTotalSeparateAmount(secondPayAmount);
    secondPeriodSeparate.setSeparateState(SeparateStateEnum.PROCESSING.getCode());
    
    // 【核心修正】复用第一期的清分主体配置，重新计算分账金额
    List<PlusOrderSeparateItemEntity> secondPeriodItems = createSecondPeriodSeparateItems(
            firstPeriodItems, secondPeriodSeparate, secondPayAmount);
    
    secondPeriodSeparate.setItems(secondPeriodItems);
    
    // 构建返回结果
    PlusOrderDeductResEntity result = new PlusOrderDeductResEntity();
    result.setOptStatus(DeductResultStateEnum.SUCCESS.getCode());
    result.setSeparateEntity(secondPeriodSeparate);
    result.setRemark("第二期支付分账信息生成成功");
    
    return result;
}
```

#### 2.3 清分主体明细复用逻辑
**新增方法**：`createSecondPeriodSeparateItems`

```java
private List<PlusOrderSeparateItemEntity> createSecondPeriodSeparateItems(
        List<PlusOrderSeparateItemEntity> firstPeriodItems, 
        PlusOrderSeparateEntity secondPeriodSeparate, 
        BigDecimal secondPayAmount) {
    
    List<PlusOrderSeparateItemEntity> secondPeriodItems = new ArrayList<>();
    
    for (PlusOrderSeparateItemEntity firstItem : firstPeriodItems) {
        PlusOrderSeparateItemEntity secondItem = new PlusOrderSeparateItemEntity();
        
        // 【核心功能】复用第一期的配置信息
        secondItem.setOrderSn(secondPeriodSeparate.getOrderSn());
        secondItem.setApplySerialNo(secondPeriodSeparate.getApplySerialNo());
        secondItem.setSupplierType(firstItem.getSupplierType());        // 复用主体类型
        secondItem.setSupplierId(firstItem.getSupplierId());            // 复用主体ID
        secondItem.setMerchantId(firstItem.getMerchantId());            // 复用商务识别码
        secondItem.setSeparateType(firstItem.getSeparateType());        // 复用分账方式
        secondItem.setSeparateRate(firstItem.getSeparateRate());        // 复用分账比例
        
        // 【核心功能】重新计算分账金额
        BigDecimal newSeparateAmount;
        if (SeparateTypeEnum.RATE.getCode().equals(firstItem.getSeparateType())) {
            // 按比例分账：第二期金额 * 分账比例
            newSeparateAmount = secondPayAmount.multiply(firstItem.getSeparateRate())
                    .setScale(2, RoundingMode.HALF_UP);
        } else {
            // 固定金额分账：使用第一期的固定金额（可根据业务需求调整）
            newSeparateAmount = firstItem.getSeparateAmount();
        }
        
        secondItem.setSeparateAmount(newSeparateAmount);
        secondItem.setRemark("第二期支付分账明细，复用第一期清分主体配置");
        
        secondPeriodItems.add(secondItem);
    }
    
    return secondPeriodItems;
}
```

### 3. 修正效果

#### 3.1 数据一致性保证
- **分流主体一致性**：第二期支付使用与第一期完全相同的 `shuntSupplierId`、`businessScene` 等配置
- **清分主体一致性**：第二期支付使用与第一期完全相同的 `supplierType`、`merchantId`、`separateType`、`separateRate` 等配置
- **金额计算准确性**：根据第二期支付金额和第一期的分账比例重新计算分账金额

#### 3.2 业务流程完整性
- **第一期支付**：正常生成和保存分流清分信息
- **第二期支付**：查询并复用第一期的分流清分配置，确保两期支付的分账处理完全一致

#### 3.3 异常处理完善
- 校验第一期分账记录是否存在
- 校验第一期清分主体明细是否存在
- 校验第二期支付金额计算的合法性

### 4. 关键技术点

#### 4.1 清分主体类型识别
```java
// 通过 supplierType 识别清分主体
if (SupplierTypeEnum.QF.getCode().equals(item.getSupplierType())) {
    // 这是清分主体记录
}
```

#### 4.2 分账金额计算策略
- **比例分账**：`第二期金额 × 分账比例`
- **固定金额分账**：根据业务需求决定是否按比例调整

#### 4.3 申请流水号生成
```java
private String getApplySerialNo(OrderPayActionEnum payAction, String orderSn) {
    return payAction.getCode() + "_" + orderSn + "_" + System.currentTimeMillis();
}
```

### 5. 测试验证要点

#### 5.1 功能测试
- 验证第一期支付的分流清分信息正确保存
- 验证第二期支付正确查询和复用第一期的配置
- 验证第二期支付的分账金额计算准确性

#### 5.2 数据一致性测试
- 对比第一期和第二期的分流主体配置
- 对比第一期和第二期的清分主体配置
- 验证分账金额的计算逻辑

#### 5.3 异常场景测试
- 第一期支付未完成时发起第二期支付
- 第一期分账记录不存在的异常处理
- 第一期清分主体记录不存在的异常处理

## 总结

通过本次修正，解决了第二期支付清分主体逻辑的缺陷，确保了分期支付两期的分流清分处理完全一致。修正后的实现具备以下特点：

1. **完整的数据复用**：第二期支付完全复用第一期的分流清分配置
2. **准确的金额计算**：根据第二期支付金额重新计算分账金额
3. **完善的异常处理**：覆盖各种异常场景
4. **清晰的业务逻辑**：代码结构清晰，易于理解和维护

修正后的功能能够确保分期支付的分账处理完全符合业务需求，保证了数据的一致性和准确性。
