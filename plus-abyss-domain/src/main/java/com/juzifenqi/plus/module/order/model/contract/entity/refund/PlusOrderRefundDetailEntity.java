package com.juzifenqi.plus.module.order.model.contract.entity.refund;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员订单退款明细（用于首期支付）
 *
 * <AUTHOR>
 * @date 2024/9/2 11:06
 */
@Data
public class PlusOrderRefundDetailEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 退款记录id
     */
    private Long refundInfoId;

    /**
     * 原支付流水号
     */
    private String originalPaySerialNo;

    /**
     * 退款业务流水号
     */
    private String refundSerialNo;

    /**
     * 支付退款流水号
     */
    private String paySerialNo;

    /**
     * 退款状态 1_退款中 2_退款成功 3_退款失败
     */
    private Integer refundState;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 支付回调时间
     */
    private Date payCallbackTime;

    /**
     * 退款总期数
     */
    private Integer totalPeriod;
    /**
     * 当前退款期数
     */
    private Integer currentPeriod;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
