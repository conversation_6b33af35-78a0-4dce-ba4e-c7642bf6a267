package com.juzifenqi.plus.module.order.model.impl;

import com.juzifenqi.plus.module.order.model.IPlusOrderRefundInfoModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRefundInfoRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;

import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会员订单退款信息
 *
 * <AUTHOR>
 * @date 2024/9/2 11:20
 */
@Slf4j
@Service
public class PlusOrderRefundInfoModelImpl implements IPlusOrderRefundInfoModel {

    @Autowired
    private IPlusOrderRefundInfoRepository plusOrderRefundInfoRepository;

    @Override
    public List<PlusOrderRefundInfoEntity> getByOrderSn(String orderSn,
            List<Integer> refundStateList) {
        return plusOrderRefundInfoRepository.getByOrderSn(orderSn, refundStateList);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Long saveOrderRefundInfo(PlusOrderRefundInfoEntity entity) {
        return plusOrderRefundInfoRepository.save(entity);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Long saveOrderRefundInfoAndDetail(PlusOrderRefundInfoEntity entity,List<PlusOrderRefundDetailEntity> details) {
        return plusOrderRefundInfoRepository.saveInfoAndDetail(entity, details);
    }

    @Override
    public PlusOrderRefundInfoEntity getById(Long refundInfoId) {
        return plusOrderRefundInfoRepository.getById(refundInfoId);
    }

    @Override
    public PlusOrderRefundInfoEntity getByRefundSerialNo(String refundSerialNo) {
        return plusOrderRefundInfoRepository.getByRefundSerialNo(refundSerialNo);
    }

    @Override
    public PlusOrderRefundDetailEntity getDetailByRefundSerialNo(String refundSerialNo) {
        return plusOrderRefundInfoRepository.getDetailByRefundSerialNo(refundSerialNo);
    }

    @Override
    public List<PlusOrderRefundDetailEntity> getDetailsByInfoId(Long refundInfoId) {
        return plusOrderRefundInfoRepository.getDetailsByInfoId(refundInfoId);
    }

    @Override
    public void updateRefundState(String refundSerialNo, Integer refundState) {
        plusOrderRefundInfoRepository.updateRefundState(refundSerialNo, refundState);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateRefundInfoAndDetailState(Long refundInfoId, List<Long> refundDetailIds, Integer refundState) {
        plusOrderRefundInfoRepository.updateRefundInfoAndDetailState(refundInfoId, refundDetailIds, refundState);
    }

    @Override
    public void updateRefundDetailInfo(Long detailId, Integer refundState, String paySerialNo) {
        plusOrderRefundInfoRepository.updateRefundDetailInfo(detailId, refundState, paySerialNo);
    }

    @Override
    public void updateOrderRefundInfo(PlusOrderRefundInfoEntity entity) {
        plusOrderRefundInfoRepository.updateById(entity);
    }

    @Override
    public void updateOrderRefundDetail(PlusOrderRefundDetailEntity entity) {
        //TODO
    }

    @Override
    public void updatePaySerialNo(String refundSerialNo, String paySerialNo) {
        plusOrderRefundInfoRepository.updatePaySerialNo(refundSerialNo, paySerialNo);
    }

    @Override
    public void updatePaySerialNo(Long id, String paySerialNo) {
        plusOrderRefundInfoRepository.updatePaySerialNo(id, paySerialNo);
    }

}
