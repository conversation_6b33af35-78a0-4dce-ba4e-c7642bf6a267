package com.juzifenqi.plus.module.order.model.contract;

import com.juzifenqi.plus.enums.OrderPayActionEnum;
import com.juzifenqi.plus.enums.SeparateStateEnum;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemAdminEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemEntity;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparateItemPo;
import java.util.List;

/**
 * 分账数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/31 14:27
 */
public interface IPlusOrderSeparateRepository {

    /**
     * 保存分流信息
     */
    Long saveOrderSeparate(PlusOrderSeparateEntity saveEntity);

    /**
     * 更新分流信息
     */
    void updateOrderSeparate(PlusOrderSeparateEntity updateEntity);

    /**
     * 根据订单号、支付行为更新订单分账状态
     *
     * @param orderSn 订单号
     * @param orderPayAction 订单支付行为
     * @see OrderPayActionEnum 订单支付行为枚举
     * @param separateState 分账状态 1_分账处理中 2_分账成功 3_分账失败
     * @see SeparateStateEnum 分账状态枚举
     * @return 更新数据条数
     */
    Integer updateOrderSeparateState(String orderSn, int orderPayAction, int separateState);

    /**
     * 通过订单号+申请流水号查询分流信息
     */
    PlusOrderSeparateEntity getOrderSeparateByApplyNo(String orderNo, String applySerialNo);

    /**
     * 通过订单号查询分账记录
     */
    List<PlusOrderSeparateEntity> getOrderSeparateByOrderNo(String orderNo);
    /**
     * 通过订单号+分账状态查询分流信息
     */
    PlusOrderSeparateEntity getOrderSeparateByStatus(String orderNo, Integer separateState);

    /**
     * 查询分流明细
     */
    List<PlusOrderSeparateItemEntity> getPlusOrderSeparateItems(PlusOrderSeparateItemPo po);

    /**
     * 通过订单号查询最近一条分账记录
     */
    PlusOrderSeparateEntity getLastByOrderSn(String orderSn);

    /**
     * 查询订单清分明细
     */
    List<PlusOrderSeparateItemAdminEntity> getOrderSeparateItemList(String orderSn,
            Integer separateEnableState);

    /**
     * 根据订单号和支付动作查询分账明细
     */
    List<PlusOrderSeparateItemEntity> getSeparateItemsByOrderSnAndPayAction(String orderSn,
            Integer orderPayAction);
}
