package com.juzifenqi.plus.module.order.repository.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderSeparateRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemAdminEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemEntity;
import com.juzifenqi.plus.module.order.repository.converter.IPlusOrderSeparateRepositoryConverter;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderSeparateItemMapper;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderSeparateMapper;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparateItemPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparatePo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 分账实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/31 14:28
 */
@Service
@Slf4j
public class PlusOrderSeparateRepositoryImpl implements IPlusOrderSeparateRepository {

    private final IPlusOrderSeparateRepositoryConverter converter = IPlusOrderSeparateRepositoryConverter.instance;

    @Autowired
    private IPlusOrderSeparateMapper     separateMapper;
    @Autowired
    private IPlusOrderSeparateItemMapper separateItemMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Long saveOrderSeparate(PlusOrderSeparateEntity saveEntity) {
        log.info("保存订单分账信息：{}", JSON.toJSONString(saveEntity));
        PlusOrderSeparatePo po = converter.toPlusOrderSeparatePo(saveEntity);
        separateMapper.savePlusOrderSeparate(po);
        separateItemMapper.batchInsert(
                converter.toPlusOrderSeparateItemPoList(saveEntity.getItems()));
        return po.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderSeparate(PlusOrderSeparateEntity updateEntity) {
        log.info("更新订单分账信息：{}", JSON.toJSONString(updateEntity));
        separateMapper.updatePlusOrderSeparate(converter.toPlusOrderSeparatePo(updateEntity));
    }

    @Override
    public Integer updateOrderSeparateState(String orderSn, int orderPayAction, int separateState) {
        log.info("更新订单分账信息分账状态:{}, {}, {}", orderSn, orderPayAction, separateState);
        return separateMapper.updatePlusOrderSeparateState(orderSn, orderPayAction, separateState);
    }

    /**
     * 通过申请流水号查询分流信息
     */
    @Override
    public PlusOrderSeparateEntity getOrderSeparateByApplyNo(String orderSn, String applySerialNo) {
        return converter.toPlusOrderSeparateEntity(
                separateMapper.getOrderSeparateByApplyNo(orderSn, applySerialNo));
    }

    /**
     * 通过订单号查询分流信息
     */
    @Override
    public List<PlusOrderSeparateEntity> getOrderSeparateByOrderNo(String orderSn) {
        return converter.toPlusOrderSeparateEntityList(
                separateMapper.getOrderSeparateByOrderNo(orderSn));
    }

    /**
     * 通过订单号+分账状态查询分流信息
     */
    @Override
    public PlusOrderSeparateEntity getOrderSeparateByStatus(String orderSn, Integer separateState) {
        return converter.toPlusOrderSeparateEntity(
                separateMapper.getOrderSeparateByStatus(orderSn, separateState));
    }

    /**
     * 查询分流明细
     */
    @Override
    public List<PlusOrderSeparateItemEntity> getPlusOrderSeparateItems(PlusOrderSeparateItemPo po) {
        return converter.toPlusOrderSeparateItemEntityList(
                separateItemMapper.getPlusOrderSeparateItems(po));
    }

    @Override
    public PlusOrderSeparateEntity getLastByOrderSn(String orderSn) {
        return converter.toPlusOrderSeparateEntity(separateMapper.getLastByOrderSn(orderSn));
    }

    /**
     * 查询订单清分明细
     */
    @Override
    public List<PlusOrderSeparateItemAdminEntity> getOrderSeparateItemList(String orderSn,
            Integer separateEnableState) {
        return separateItemMapper.getOrderSeparateItemList(orderSn, separateEnableState);
    }

    /**
     * 根据订单号和支付动作查询分账明细
     */
    @Override
    public List<PlusOrderSeparateItemEntity> getSeparateItemsByOrderSnAndPayAction(String orderSn,
            Integer orderPayAction) {
        return converter.toPlusOrderSeparateItemEntityList(
                separateItemMapper.getSeparateItemsByOrderSnAndPayAction(orderSn, orderPayAction));
    }
}
