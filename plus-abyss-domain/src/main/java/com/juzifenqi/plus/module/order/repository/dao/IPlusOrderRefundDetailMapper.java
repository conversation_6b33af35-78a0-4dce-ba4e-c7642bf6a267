package com.juzifenqi.plus.module.order.repository.dao;

import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会员订单退款信息
 *
 * <AUTHOR>
 * @date 2024/9/2 10:19
 */
@Mapper
public interface IPlusOrderRefundDetailMapper {

    /**
     * 新增返回ID
     */
    void batchInsert(List<PlusOrderRefundDetailEntity> list);


    void updateStateByIds(@Param("ids") List<Long> ids,@Param("refundState") Integer refundState);

    void updateDetail(@Param("id") Long id,@Param("refundState")Integer refundState, @Param("paySerialNo") String paySerialNo);

    PlusOrderRefundDetailEntity getDetailByRefundSerialNo(@Param("refundSerialNo") String refundSerialNo);

    List<PlusOrderRefundDetailEntity> getDetailsByInfoId(@Param("refundInfoId") Long refundInfoId);
}
