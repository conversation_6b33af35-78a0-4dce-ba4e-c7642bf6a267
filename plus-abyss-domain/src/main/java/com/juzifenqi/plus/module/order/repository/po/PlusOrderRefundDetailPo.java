package com.juzifenqi.plus.module.order.repository.po;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员订单退款信息
 *
 * <AUTHOR>
 * @date 2024/9/2 10:15
 */
@Data
public class PlusOrderRefundDetailPo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 退款记录id
     */
    private Integer refundInfoId;

    /**
     *
     */
    private String originalPaySerialNo;

    /**
     * 退款业务流水号
     */
    private String refundSerialNo;

    /**
     * 支付退款流水号
     */
    private String paySerialNo;

    /**
     * 退款状态 0-待退款 1_退款中 2_退款成功 3_退款失败
     */
    private Integer refundState;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 退款结果回调时间
     */
    private Date payCallbackTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
