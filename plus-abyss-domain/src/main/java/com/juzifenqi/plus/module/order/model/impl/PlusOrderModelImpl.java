package com.juzifenqi.plus.module.order.model.impl;

import static com.juzifenqi.plus.constants.CommonConstant.TWO;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.order.dao.entity.Orders;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.EngineCodeConstant;
import com.juzifenqi.plus.constants.EngineResultConstant;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.enums.*;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.MemberUseRecordModel;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusInfoRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ProductCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualGoodsCheckResultEntity;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo;
import com.juzifenqi.plus.module.asserts.repository.po.MemberUseProductRecordPo;
import com.juzifenqi.plus.module.common.IEngineExternalRepository;
import com.juzifenqi.plus.module.common.IMallTradeExternalRepository;
import com.juzifenqi.plus.module.common.IMemberExternalRepository;
import com.juzifenqi.plus.module.common.IMemberPlusSystemLogRepository;
import com.juzifenqi.plus.module.common.IPlusOrderExternalRepository;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.common.IProductExternalRepository;
import com.juzifenqi.plus.module.common.entity.DistributionMarketEntity;
import com.juzifenqi.plus.module.common.entity.MemberPlusSystemLogEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.plus.module.market.model.IPlusMarketModel;
import com.juzifenqi.plus.module.order.model.IPlusPayRecordModel;
import com.juzifenqi.plus.module.order.model.PlusOrderModel;
import com.juzifenqi.plus.module.order.model.contract.IInvalidOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderBillRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderContractRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderDeductRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRelationRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderSeparateRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusRepeatOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.OrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusDiscountEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOpenResultEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductResEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderRelationEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderResubmitFlagEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRdzxRelationDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.bill.PlusOrderBillEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.repeat.MemberPlusRepeatOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.converter.IPlusOrderModelConverter;
import com.juzifenqi.plus.module.order.model.event.CreateDeductPlanEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderContractEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayCallbackEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRelationCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusProductOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.UpdOrderStateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualGoodsOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.impl.factory.PlusOrderRelationFactory;
import com.juzifenqi.plus.module.order.model.impl.strategy.HandlerContext;
import com.juzifenqi.plus.module.order.repository.external.acl.VirtualExternalRepositoryAcl;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo;
import com.juzifenqi.plus.mq.producer.PlusMqProducer;
import com.juzifenqi.plus.utils.RedisUtils;
import com.juzifenqi.plus.utils.SwitchUtil;
import com.juzifenqi.product.entity.Product;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PlusOrderModelImpl implements PlusOrderModel {

    @Autowired
    private IOrderExternalRepository       orderExternalRepository;
    @Autowired
    private IPlusOrderRepository           orderRepository;
    @Autowired
    private IPlusOrderExternalRepository   plusOrderExternalRepository;
    @Autowired
    private MemberPlusQueryModel           memberPlusQueryModel;
    @Autowired
    private IMemberPlusSystemLogRepository memberPlusSystemLogRepository;
    @Autowired
    private IMemberExternalRepository      memberExternalRepository;
    @Autowired
    private IPlusOrderContractRepository   plusOrderContractRepository;
    @Autowired
    private RedisUtils                     redisUtils;
    @Autowired
    private PlusOrderRelationFactory       plusOrderRelationFactory;
    @Autowired
    private IPlusOrderRelationRepository   plusOrderRelationRepository;
    @Autowired
    private IPlusOrderDeductRepository     plusOrderDeductRepository;
    @Autowired
    private IMemberPlusInfoRepository      memberPlusInfoRepository;
    @Autowired
    private IMemberPlusSystemLogRepository logRepository;
    @Autowired
    private HandlerContext                 handlerContext;
    @Autowired
    private IInvalidOrderRepository        invalidOrderRepository;
    @Autowired
    private VirtualExternalRepositoryAcl   virtualExternalRepositoryAcl;
    @Resource
    private IMallTradeExternalRepository   mallTradeExternalRepository;
    @Resource
    private IProductExternalRepository     productExternalRepository;
    @Resource
    private MemberUseRecordModel           useRecordModel;
    @Autowired
    private IPlusOrderBillRepository       orderBillRepository;
    @Autowired
    private IMemberPlusSystemLogRepository systemLogRepository;
    @Autowired
    private IPlusPayRecordModel            payRecordModel;
    @Autowired
    private IEngineExternalRepository      engineExternalRepository;
    @Autowired
    private IPlusShuntRepository           shuntRepository;
    @Autowired
    private SwitchUtil                     switchUtil;
    @Resource
    private IPlusOrderSeparateRepository   orderSeparateRepository;
    @Autowired
    private IPlusRepeatOrderRepository     repeatOrderRepository;
    @Autowired
    private PlusMqProducer plusMqProducer;
    @Autowired
    private IPlusMarketModel iPlusMarketModel;

    private final IPlusOrderModelConverter converter = IPlusOrderModelConverter.instance;

    /**
     * 1.下单
     */
    @Override
    public PlusOrderEntity createPlusOrder(PlusOrderCreateEvent plusOrderCreateEvent,
            PlusProgramEntity plusProgramEntity) {
        Integer userId = plusOrderCreateEvent.getUserId();
        PlusDiscountEntity discount = getPlusDiscount(userId, plusProgramEntity.getConfigId(), plusOrderCreateEvent.getChannelId(), plusOrderCreateEvent.getSceneCode());
        BigDecimal discountRate = discount != null ? discount.getDiscountRate() : null;
        // 计算会员订单金额
        BigDecimal plusOrderAmount = plusOrderExternalRepository.calPlusOrderAmount(
                plusOrderCreateEvent, plusProgramEntity, discountRate);

        // 计算首付金额（分期支付场景）
        BigDecimal firstPayAmount = null;
        if (PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue().equals(plusOrderCreateEvent.getPayType())) {
            // 首付金额 = Math.floor(会员方案原价 * 折扣比例)
            BigDecimal originalPrice = plusProgramEntity.getFirstPayAmount();
            BigDecimal discountedPrice = discountRate != null ?
                    originalPrice.multiply(discountRate) : originalPrice;
            firstPayAmount = discountedPrice.setScale(0, RoundingMode.DOWN);
        }

        // 转换
        PlusOrderEntity plusOrderEntity = converter.toOrderEntity(plusOrderCreateEvent,
                plusOrderAmount, discountRate, plusProgramEntity);

        // 设置首付金额
        if (firstPayAmount != null) {
            plusOrderEntity.setFirstPayAmount(firstPayAmount);
        }
        // 调用订单中心创单
        OrderEntity orderEntity = orderExternalRepository.emberOrderCommit(plusOrderEntity,
                plusOrderCreateEvent);
        plusOrderEntity.setOrderSn(orderEntity.getOrderId());
        plusOrderEntity.setSign(orderEntity.getSign());
        plusOrderEntity.setOrderAmount(orderEntity.getMoneyOrder());
        // 查询会员身份
        MemberPlusInfoEntity memberPlusInfo = memberPlusQueryModel.getMemberPlusInfo(
                userId, plusOrderCreateEvent.getChannelId(),
                plusProgramEntity.getConfigId());
        orderRepository.saveOrder(plusOrderEntity, memberPlusInfo, plusOrderCreateEvent);
        // 创单日志
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        plusLog.memberId(userId)
                .channelId(plusOrderCreateEvent.getChannelId())
                .programId(plusOrderCreateEvent.getProgramId())
                .orderSn(plusOrderEntity.getOrderSn());
        logRepository.saveMemberPlusLogByUser(plusLog, LogNodeEnum.LOG_NODE_ORDER_SUBMIT);
        PlusOrderPayTypeEnum payType = PlusOrderPayTypeEnum.getByValue(
                plusOrderCreateEvent.getPayType());
        // 后付款创单日志
        if (payType != null && payType.getPayAfter()) {
            logRepository.saveMemberPlusLogByUser(plusLog, LogNodeEnum.LOG_NODE_OPEN_AFTER_ORDER);
        }
        return plusOrderEntity;
    }

    public PlusDiscountEntity getPlusDiscount(Integer userId, Integer configId, Integer channelId, Integer sceneCode) {
        DistributionMarketEntity disResult = null;
        //首页购买加速卡下单 走新折扣,需获取营销样式。首页营销加速卡折扣逻辑保持一致。@see PlusMarketModelImpl.getDiscount
        if (configId == JuziPlusEnum.EXPEDITE_CARD.getCode()) {
            log.info("借款首页加速卡下单获取营销样式：{},{}", userId, channelId);
            // 获取611或501状态借款单
            Orders order = orderExternalRepository.lastOneProcessOrder(userId);
            // 桔策营销样式
            disResult = engineExternalRepository.getDistributionMarketResult(
                    order != null ? order.getOrderSn() : null, userId, EngineCodeConstant.VIP_DOWN, channelId);
        }
        // 计算客群折扣
        return iPlusMarketModel.getDiscount(userId, channelId, configId, sceneCode, disResult);
    }

    @Override
    public OrderEntity createVirtualOrder(VirtualOrderCreateEvent createEvent,
            VirtualCheckResultEntity entity) {
        // 订单金额
        BigDecimal orderAmount = plusOrderExternalRepository.calVirtualOrderAmount(createEvent,
                entity.getDiscountRate());
        createEvent.setOrderAmount(orderAmount);
        // 提交订单到订单服务
        OrderEntity order = orderExternalRepository.virtualOrderCommit(createEvent);
        // 提交订单到虚拟权益服务
        virtualExternalRepositoryAcl.createVirtualOrder(createEvent, entity, order);
        // 20240104 zjf 0元虚拟权益订单下单成功后删除权益页-生活权益/渠道生活权益列表的缓存
        if (order.getMoneyOrder().compareTo(BigDecimal.ZERO) == 0) {
            if (Objects.equals(createEvent.getModelId(), PlusModelEnum.SHQY.getModelId())) {
                // 生活权益key
                String virtualKey =
                        RedisConstantPrefix.MEMBER_VIRTUAL_PRODUCTS + createEvent.getProgramId()
                                + "_" + createEvent.getUserId() + "_" + order.getOrderId();
                redisUtils.delete(virtualKey);
                log.info("0元虚拟权益下单成功删除权益页生活权益列表缓存完成：{}", virtualKey);
            } else {
                // 渠道生活权益key
                String qdVirtualKey =
                        RedisConstantPrefix.MEMBER_VIRTUAL_PRODUCTS + createEvent.getProgramId()
                                + "_" + createEvent.getModelId() + "_" + createEvent.getUserId()
                                + "_" + order.getOrderId();
                redisUtils.delete(qdVirtualKey);
                log.info("0元虚拟权益下单成功删除权益页渠道生活权益列表缓存完成：{}", qdVirtualKey);
            }
        }
        return order;
    }

    /**
     * 虚拟商品权益下单
     */
    @Override
    public OrderEntity createVirtualGoodsOrder(VirtualGoodsOrderCreateEvent createEvent,
            VirtualGoodsCheckResultEntity entity) {
        // 订单金额
        BigDecimal orderAmount = plusOrderExternalRepository.calVirtualGoodsOrderAmount(createEvent,
                entity.getDiscountRate());
        createEvent.setOrderAmount(orderAmount);
        // 提交订单到订单服务
        VirtualOrderCreateEvent orderCreateEvent = converter.toVirtualOrderCreateEvent(createEvent);
        OrderEntity order = orderExternalRepository.virtualOrderCommit(orderCreateEvent);
        // 提交订单到虚拟权益服务
        VirtualCheckResultEntity checkResult = new VirtualCheckResultEntity();
        checkResult.setPlusOrderSn(createEvent.getPlusOrderSn());
        virtualExternalRepositoryAcl.createVirtualOrder(orderCreateEvent, checkResult, order);
        return order;
    }

    @Override
    public OrderEntity createPlusProductOrder(PlusProductOrderCreateEvent createEvent,
            ProductCheckResultEntity checkResult) {
        PlusProgramProductNewPo productNewPo = checkResult.getProductNewPo();
        if (productNewPo == null) {
            throw new PlusAbyssException("获取商品分类配置失败,请重试");
        }
        // 查询商品信息
        Product product = productExternalRepository.getProductById(createEvent.getProductId());
        if (product == null) {
            throw new PlusAbyssException("获取商品信息失败,请重试");
        }
        // 折扣处理
        BigDecimal discount = productNewPo.getDiscountRate();
        // 商品数量写死1
        createEvent.setProductNum(1);
        // 计算金额
        BigDecimal orderAmount = plusOrderExternalRepository.calPlusProductOrderAmount(createEvent,
                discount, product.getMallMobilePrice());
        createEvent.setSellerId(product.getSellerId());
        // 请求下单
        OrderEntity orderEntity = null;
        try {
            orderEntity = mallTradeExternalRepository.commitOrderInfo(createEvent);
            // 保存关联关系
            saveProductRecord(productNewPo, createEvent, orderAmount, orderEntity, product);
        } catch (Exception e) {
            // 非业务异常保存关联关系，防止超卖
            if (!(e instanceof PlusAbyssException)) {
                saveProductRecord(productNewPo, createEvent, orderAmount, orderEntity, product);
            }
            LogUtil.printLog(e, "调用商品订单接口创单异常");
            throw e;
        }
        return orderEntity;
    }

    /**
     * 保存商品关联关系记录
     */
    private void saveProductRecord(PlusProgramProductNewPo productNewPo,
            PlusProductOrderCreateEvent createEvent, BigDecimal orderAmount,
            OrderEntity orderEntity, Product product) {
        MemberUseProductRecordPo po = converter.toMemberUseProductRecordPo(createEvent,
                productNewPo, orderAmount, new Date(),
                orderEntity != null ? orderEntity.getOrderId() : null, product);
        useRecordModel.saveUserBuyOrderRecord(po);
        // 0元商品删除权益页缓存
        if (orderAmount.compareTo(BigDecimal.ZERO) == 0) {
            redisUtils.delete(
                    RedisConstantPrefix.MEMBER_PLUS_PRODUCTS + createEvent.getOrderSn() + "_"
                            + createEvent.getUserId());
            log.info("提交会员商品订单后删除权益页缓存成功：{}", createEvent.getOrderSn());
        }
    }

    /**
     * 2.2 支付结果回调
     */
    @Override
    public void payCallBackForOpenCard(PlusOrderPayCallbackEvent plusPayCallbackEvent,
            PlusOrderEntity plusOrderEntity) {
        // 获取会员身份信息
        MemberPlusInfoDetailEntity memberPlusInfoDetail = memberPlusQueryModel.getMemberPlusInfoDetail(
                plusPayCallbackEvent.getOrderSn());
        // 订单支付成功处理
        orderRepository.orderPaySuccess(plusPayCallbackEvent, plusOrderEntity,
                memberPlusInfoDetail);
        // 购买记录缓存
        payRecordCache(plusOrderEntity);
    }


    /**
     * 获取购买记录
     */
    private void payRecordCache(PlusOrderEntity plusOrderEntity) {
        Integer userId = plusOrderEntity.getUserId();
        String text = CommonConstant.DEFAULT_RECORD;
        try {
            // 获取用户信息
            MemberInfo memberInfo = memberExternalRepository.getById(userId);
            text = "用户" + memberInfo.getName() + "已开通";
        } catch (Exception e) {
            LogUtil.printLog("拼接购买记录，获取用户信息失败，userId：{}", userId);
        }
        try {
            //存到redis中
            String redisKey =
                    RedisConstantPrefix.MEMBER_PLUS_BUY_RECORD + plusOrderEntity.getProgramId();
            //放到头部
            redisUtils.lLeftPush(redisKey, text);
            //移除尾部
            redisUtils.lRightPop(redisKey);
        } catch (Exception e) {
            LogUtil.printLog("最新购买记录放入缓存发生异常", e);
        }
    }


    /**
     * 取消订单
     */
    @Override
    public void cancelOrder(PlusOrderCancelEvent event, PlusOrderEntity plusOrderEntity,
            PlusOrderCancelEntity entity, List<MemberPlusInfoDetailExtPo> moveList) {
        // 20240909 ltq 清分：取消订单新老逻辑兼容
        if (!switchUtil.isNew(event.getUserId())) {
            // 取消订单中心订单
            orderExternalRepository.closeOrder(event, plusOrderEntity, entity);
        }
        // 取消会员订单
        orderRepository.cancelOrder(event, entity);
        // 取消相关计划
        orderRepository.cancelPlan(event, plusOrderEntity);
        // 个性化（会员卡维度）
        handlerContext.cancelOrderAfter(plusOrderEntity, event);
        // 个性化（取消方式维度）
        handlerContext.cancelOrderAfterByType(plusOrderEntity, event, entity);
        // 移动订单周期
        orderRepository.moveOrderPeriod(moveList);
        // 操作日志
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        plusLog.memberId(event.getUserId()).channelId(event.getChannelId())
                .programId(event.getProgramId()).orderSn(event.getPlusOrderSn())
                .reason(event.getCancelReason())
                .cancelRemark("取消成功," + getCancelRemark(plusOrderEntity, event))
                .optId(event.getOptUserId()).optName(event.getOptUserName())
                .useProfit(event.getUseProfit());
        memberPlusSystemLogRepository.saveCancelLogByUser(plusLog, getCancelOrderNode(event));
    }

    @Override
    public void cancelRepOrder(PlusOrderCancelEvent event, PlusOrderCancelEntity entity,
            PlusOrderEntity plusOrderEntity) {
        // 取消会员订单
        orderRepository.cancelOrder(event, entity);
        // 取消相关计划
        orderRepository.cancelPlan(event, plusOrderEntity);
    }

    /**
     * 获取取消操作节点
     */
    public LogNodeEnum getCancelOrderNode(PlusOrderCancelEvent event) {
        PlusCancelTypeEnum cancelType = PlusCancelTypeEnum.getByValue(event.getCancelType());
        switch (cancelType) {
            case JS:
            case DELAY:
                return LogNodeEnum.LOG_NODE_JSTK_CANCEL_SYSTEM;
            case NO_CONDITION:
                return event.isBatchOpt() ? LogNodeEnum.LOG_NODE_BATCH_CANCEL
                        : LogNodeEnum.LOG_NODE_UN_CONDITION_CANCEL;
            case RATIO:
                return LogNodeEnum.LOG_NODE_RATIO_CANCEL;
            case EXPIRE_PAYMENT:
                return LogNodeEnum.LOG_NODE_PAST_REFUND;
            case NO_EXPIRE_PAYMENT:
                return LogNodeEnum.LOG_NODE_DE_CANCEL;
            default:
                return LogNodeEnum.LOG_NODE_CANCEL;
        }
    }

    /**
     * 获取出账备注
     */
    @Override
    public String getCancelRemark(PlusOrderEntity plusOrderInfo, PlusOrderCancelEvent event) {
        String cancelTypeStr = Objects.requireNonNull(
                PlusCancelTypeEnum.getByValue(event.getCancelType())).getName();
        String cancelRemark = cancelTypeStr;
        if (StringUtils.isNotBlank(event.getRemark())) {
            cancelRemark = event.getRemark() + "-" + cancelTypeStr;
        }
        Integer cancelType = event.getCancelType();
        // 后付款待支付的订单 或 极速退款/延迟退款 或 非分流的卡，不增加出账备注
        if ((Objects.equals(plusOrderInfo.getPayType(), PlusOrderPayTypeEnum.PAY_AFTER.getValue())
                && plusOrderInfo.getOrderState() == PlusOrderStateEnum.WAIT_PAY.getCode()) || (
                cancelType == PlusCancelTypeEnum.JS.getValue()
                        || cancelType == PlusCancelTypeEnum.DELAY.getValue())
                || !PlusConstant.SHUNT_CARD_LIST.contains(plusOrderInfo.getConfigId())) {
            return cancelRemark;
        }
        PlusOrderBillEntity plusOrderBill = orderBillRepository.getByOrderSn(
                plusOrderInfo.getOrderSn());
        if (plusOrderBill == null || plusOrderBill.getInSupplier() == null) {
            // 不进入对账表的，由桔子出账
            return cancelRemark + ",该笔退款由桔子出账";
        }
        Integer inSupplier = plusOrderBill.getInSupplier();
        // 代付由桔子出账（实际入账方=黑卡或桔子）
        if (!PlusCancelTypeEnum.isNotDefrayCancel(cancelType)
                && inSupplier == SupplierEnum.XSHK.getCode()) {
            return cancelRemark + ",该笔退款由桔子出账";
        }
        return cancelRemark + ",该笔退款由" + getSupplierName(inSupplier) + "出账";
    }

    /**
     * 获取分流主体名称
     */
    private String getSupplierName(Integer supplierId) {
        PlusShuntSupplierEntity supplierCache = shuntRepository.getSupplierCache(supplierId);
        if (supplierCache == null) {
            // 兼容上线期间，未配置分流主体的情况
            return SupplierEnum.getName(supplierId);
        }
        return supplierCache.getSupplierName();
    }

    @Override
    public void updOrderByCustomer(UpdOrderStateEvent event, PlusOrderEntity order,
            List<MemberPlusInfoDetailExtPo> moveList) {
        log.info("客服操作变更会员订单状态处理开始：{}", event.getOrderSn());
        // 变更订单中心状态
        orderExternalRepository.customerUpdateOrderState(event);
        PlusOrderCancelEvent plusOrderCancelEvent = converter.toPlusOrderCancelEvent(event, order,
                PlusCancelTypeEnum.CUSTOMER_CANCEL.getValue(), false);
        PlusOrderCancelEntity cancelEntity = new PlusOrderCancelEntity();
        cancelEntity.setMoneyBack(event.getRefundAmount());
        // 修改为支付成功不需要做这些逻辑
        if (event.getOrderState() == PlusOrderStateEnum.CANCELED.getCode()) {
            log.info("变更为取消处理开始：{}", event.getOrderSn());
            // 取消会员订单
            orderRepository.cancelOrder(plusOrderCancelEvent, cancelEntity);
            // 个性化（会员卡维度）
            handlerContext.cancelOrderAfter(order, plusOrderCancelEvent);
            // 个性化（取消方式维度）
            handlerContext.cancelOrderAfterByType(order, plusOrderCancelEvent, cancelEntity);
            // 移动订单周期
            orderRepository.moveOrderPeriod(moveList);
        } else {
            log.info("变更为支付成功处理开始：{}", event.getOrderSn());
            // 变更会员状态
            orderRepository.updateOrderState(event.getOrderSn(), event.getOrderState());
        }
        // 取消相关计划（支付成功或者取消都需要执行）
        orderRepository.cancelPlan(plusOrderCancelEvent, order);
        // 增加操作日志
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        plusLog.memberId(order.getUserId()).channelId(order.getChannelId())
                .programId(order.getProgramId()).orderSn(order.getOrderSn())
                .reason(event.getChangeReasonCode()).optId(event.getOptUserId())
                .optName(event.getOptUserName());
        logRepository.saveCancelLogByUser(plusLog, LogNodeEnum.LOG_NODE_OFFLINE_OPT);
    }

    @Override
    public void unPayCancelCallBack(String plusOrderSn) {
        try {
            log.info("会员订单未支付超时取消开始：{}", plusOrderSn);
            PlusOrderEntity order = orderRepository.getByPlusOrderSn(plusOrderSn);
            if (order == null) {
                log.info("会员订单未支付超时取消非会员订单：{}", plusOrderSn);
                return;
            }
            // 全款待支付会员单才未支付超时取消
            if (order.getOrderState() != PlusOrderStateEnum.WAIT_PAY.getCode() || Objects.equals(
                    order.getPayType(), PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
                log.info("会员订单未支付超时取消非全款支付会员订单：{}", plusOrderSn);
                return;
            }
            // 重复支付单子 不取消
            MemberPlusRepeatOrderEntity repeatOrder = repeatOrderRepository.getByOrderSn(
                    plusOrderSn);
            if (repeatOrder != null) {
                log.info("会员订单未支付超时取消,重复支付单子不处理：{}", plusOrderSn);
                return;
            }
            PlusOrderCancelEvent cancelEvent = new PlusOrderCancelEvent();
            cancelEvent.setCancelType(PlusCancelTypeEnum.UN_PAY_CANCEL.getValue());
            cancelEvent.setPlusOrderSn(plusOrderSn);
            cancelEvent.setCancelReason(CancelReasonEnum.CANCEL_REASON_20.getCode());
            orderRepository.cancelOrder(cancelEvent, null);
            // 取消支付记录
            payRecordModel.unPayCancel(plusOrderSn);
            // 将主动支付的分账数据分账状态设置为分账失败
            Integer updateNum = orderSeparateRepository.updateOrderSeparateState(plusOrderSn,
                    OrderPayActionEnum.PAY_ACTION_1.getCode(), SeparateStateEnum.FAIL.getCode());
            log.info("{}：主动支付分账状态设置为分账失败的条数：{}", plusOrderSn, updateNum);
            // 取消会员记录日志
            MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
            plusLog.memberId(order.getUserId()).channelId(order.getChannelId())
                    .programId(order.getProgramId()).orderSn(plusOrderSn)
                    .reason(CancelReasonEnum.CANCEL_REASON_20.getCode())
                    .optId(CommonConstant.STRING_SYSTEM).optName(CommonConstant.OPERATPR_NAME);
            logRepository.saveCancelLogByUser(plusLog, LogNodeEnum.LOG_NODE_CANCEL_SYSTEM);
            log.info("会员订单未支付超时取消结束：{}", plusOrderSn);
            //处理订单未支付超时关闭通知
            orderExpiredCloseNotice(plusOrderSn);
        } catch (Exception e) {
            LogUtil.printLog(e, "会员订单未支付超时取消异常");
        }
    }

    private void orderExpiredCloseNotice(String orderSn) {
        try {
            PlusOrderEntity order = orderRepository.getByPlusOrderSn(orderSn);
            if (order != null && order.getBizSource() != null && order.getBizSource() > 1) {
                PlusOrderCancelEvent cancelEvent = new PlusOrderCancelEvent();
                cancelEvent.setChannelId(order.getChannelId());
                cancelEvent.setUserId(order.getUserId());
                cancelEvent.setPlusOrderSn(orderSn);
                cancelEvent.setOutOrderSn(order.getOutOrderSn());
                cancelEvent.setBizSource(order.getBizSource());
                plusMqProducer.sendThirdSourceOrderClose(cancelEvent);
            }
        } catch (Exception e) {
            LogUtil.printLog(e, "会员订单超时关闭通知三方异常");
        }
    }

    @Override
    public void signContract(PlusOrderEntity plusOrderEntity, PlusOrderCreateEvent event,
            Integer flag) {
        try {
            log.info("签署合同开始：{}，标识：{}", JSON.toJSONString(plusOrderEntity), flag);
            switch (flag) {
                case CommonConstant.ONE:
                    PlusOrderContractEvent plusOrderContractEvent = converter.toPlusOrderContractEvent(
                            plusOrderEntity, event);
                    // 加速卡、桔享卡、固额卡、还款卡、小额月卡（后付款开通或全款开通签署合同）
                    Integer configId = plusOrderEntity.getConfigId();
                    if (configId == JuziPlusEnum.NEW_JUXP_CARD.getCode()
                            || configId == JuziPlusEnum.QUOTA_CARD.getCode()
                            || configId == JuziPlusEnum.YITONG_CARD.getCode()
                            || configId == JuziPlusEnum.REPAY_CARD.getCode()
                            || configId == JuziPlusEnum.EXPEDITE_CARD.getCode()
                            || configId == JuziPlusEnum.XEYK_CARD.getCode()) {
                        plusOrderContractRepository.signOpenCardContract(plusOrderContractEvent);
                    }
                    // 融担咨询卡（只有后付款开通的时候才签合同）
                    if (configId == JuziPlusEnum.RDZX_CARD.getCode() && event != null
                            && Objects.equals(plusOrderEntity.getPayType(),
                            PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
                        plusOrderContractRepository.signOpenCardContractRdzx(
                                plusOrderContractEvent);
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            LogUtil.printLog(e, "签署合同异常");
        }
    }

    /**
     * 会员单与业务订单建立绑定关系
     */
    @Override
    public void buildOrderRelation(PlusOrderRelationCreateEvent plusOrderRelationCreateEvent) {
        PlusOrderRelationEntity plusOrderRelationEntity = plusOrderRelationFactory.create(
                plusOrderRelationCreateEvent);
        if (Objects.nonNull(plusOrderRelationEntity)) {
            log.info("会员订单与业务订单建立绑定关系，data = {}",
                    JSON.toJSONString(plusOrderRelationEntity));
            // 保存绑定关系
            plusOrderRelationRepository.save(plusOrderRelationEntity);
        }
    }

    /**
     * 解除会员单与业务订单的绑定关系
     */
    @Override
    public void clearOrderRelation(Integer businessType, String plusOrderSn) {
        log.info("解除会员单与业务订单的绑定关系, businessType = {}, 会员单号 = {}", businessType,
                plusOrderSn);
        plusOrderRelationRepository.deleteByPlusOrderSn(businessType, plusOrderSn);
    }

    @Override
    public void createDuctPlan(CreateDeductPlanEvent createDeductPlanEvent) {
        plusOrderDeductRepository.createDuctPlan(createDeductPlanEvent);
    }

    @Override
    public void preDeduct(PlusDeductEvent deductEvent) {
        plusOrderDeductRepository.preDeduct(deductEvent);
    }

    @Override
    public PlusOrderDeductResEntity deduct(PlusDeductEvent deductEvent) {
        return handlerContext.deduct(deductEvent);
    }

    @Override
    @Deprecated
    public PlusOrderDeductResEntity deductOld(PlusDeductEvent deductEvent) {
        return handlerContext.deductOld(deductEvent);
    }

    /**
     * 取消续费
     */
    @Override
    public void cancelRenew(PlusOrderEntity plusOrderEntity, String remark) {
        MemberPlusInfoEntity entity = memberPlusInfoRepository.getMemberByConfigId(
                plusOrderEntity.getUserId(), plusOrderEntity.getChannelId(),
                plusOrderEntity.getConfigId());
        if (entity == null) {
            log.info("不存在续费标识,无法取消续费：{}", plusOrderEntity.getOrderSn());
            return;
        }
        if (entity.getRenewState() != CommonConstant.ONE) {
            log.info("已经取消续费成功,无需重复取消：{}", plusOrderEntity.getOrderSn());
            return;
        }
        memberPlusInfoRepository.updateRenewState(plusOrderEntity.getUserId(),
                plusOrderEntity.getChannelId(), plusOrderEntity.getConfigId(), TWO, remark);
    }

    @Override
    public PlusOpenResultEntity afterOrderForOpenCard(PlusOrderEntity plusOrderEntity,
            PlusOrderCreateEvent event, PlusProgramEntity program) {
        log.info("后付款会员开通成功处理：{}", plusOrderEntity.getOrderSn());
        // 获取会员身份信息
        MemberPlusInfoDetailEntity detail = memberPlusQueryModel.getMemberPlusInfoDetail(
                plusOrderEntity.getOrderSn());
        orderRepository.afterOderOpenCard(plusOrderEntity, detail);
        // 购买记录缓存
        payRecordCache(plusOrderEntity);
        // 后置处理
        return handlerContext.postProcessorAfterPlus(event, program, plusOrderEntity);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void saveInvalidOrder(Integer userId, Integer programId) {
        try {
            //后付款记录订单号
            String redisKey =
                    RedisConstantPrefix.PLUS_MEMBER_AFTER_ORDER + userId + "_" + programId;
            //查询是否已经创建了后付款订单
            String orderSn = redisUtils.get(redisKey);
            if (StringUtils.isNotBlank(orderSn)) {
                log.info("获取缓存中的后付款订单：key:{},orderSn：{}", redisKey, orderSn);
                //记录订单号
                invalidOrderRepository.saveInvalidOrder(orderSn);
                redisUtils.delete(redisKey);
            }
        } catch (Exception e) {
            log.info("用户 userId:{} 开通后付款会员保存失效订单异常 ", userId, e);
        }
    }

    @Override
    public PlusOrderCancelEntity calOrderRefundAmount(PlusOrderEntity order,
            PlusOrderCancelEvent event) {
        return orderRepository.calOrderRefundAmount(order, event);
    }

    @Override
    public void invalidOrder() {
        log.info("处理后付款开通失败订单-开始");
        //批量处理失效订单数据（订单生成成功但是会员订单保存失败的订单）
        List<String> list = invalidOrderRepository.selectInvalidOrderList();
        log.info("处理后付款开通失败订单列表：{}", list);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> cancelList = new ArrayList<>();
        // 20240507 zjf 已创单成功的单子无需处理
        for (String orderSn : list) {
            PlusOrderEntity order = orderRepository.getByPlusOrderSn(orderSn);
            if (order == null || StringUtils.isBlank(order.getOrderSn())) {
                cancelList.add(orderSn);
            }
        }
        if (!CollectionUtils.isEmpty(cancelList)) {
            orderExternalRepository.invalidOrder(cancelList);
        }
        // 20230817 zjf 去掉修改成功数量的判断，job跑批前可能客服已经取消了
        invalidOrderRepository.invalidOrder(list);
        log.info("处理后付款开通失败订单-结束");
    }

    @Override
    public void expireCancelWaitAfterPayOrder(List<String> orderSn) {
        log.info("会员过期取消待支付后付款订单开始：{}", orderSn);
        List<PlusOrderEntity> waitPayOrderList = orderRepository.getWaitPayOrderList(orderSn);
        if (CollectionUtils.isEmpty(waitPayOrderList)) {
            log.info("会员过期没有待支付的后付款订单：{}", orderSn);
            return;
        }
        // 取消会员订单
        orderRepository.expireCancelOrder(orderSn);
        // 取消订单中心的订单
        orderExternalRepository.invalidOrder(orderSn);
        // 取消相关计划、保存操作日志
        for (PlusOrderEntity order : waitPayOrderList) {
            PlusOrderCancelEvent event = converter.toPlusOrderCancelEvent(order);
            orderRepository.cancelPlan(event, order);
            MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
            plusLog.memberId(order.getUserId()).channelId(order.getChannelId())
                    .programId(order.getProgramId()).orderSn(order.getOrderSn())
                    .reason(CancelReasonEnum.CANCEL_REASON_21.getCode())
                    .optId(CommonConstant.STRING_SYSTEM).optName(CommonConstant.OPERATPR_NAME);
            systemLogRepository.saveCancelLogByUser(plusLog, LogNodeEnum.LOG_NODE_CANCEL_SYSTEM);
        }
    }

    @Override
    public void resubmitFlagVerify() {
        long start = System.currentTimeMillis();
        log.info("订单重提标识查询----------开始");
        List<PlusOrderResubmitFlagEntity> toBeVerify = orderRepository.getToBeVerifyList();
        log.info("本次主动查证重提标识的订单数量:{}", toBeVerify.size());
        if (CollectionUtils.isEmpty(toBeVerify)) {
            return;
        }
        //批量修改为处理中
        log.info("订单重提标识查询=批量处理中,开始,ids={}", JSON.toJSONString(toBeVerify));
        orderRepository.batchProcessing(toBeVerify.stream().map(PlusOrderResubmitFlagEntity::getId)
                .collect(Collectors.toList()));
        log.info("订单重提标识查询=批量处理中,结束,入库数量={}", toBeVerify.size());
        //查询标识
        toBeVerify.forEach(this::doVerify);
        long end = System.currentTimeMillis();
        log.info("订单重提标识查询==结束==耗时{}ms====", end - start);
    }

    /**
     * 订单重提标识查询
     */
    private void doVerify(PlusOrderResubmitFlagEntity record) {
        String orderSn = record.getOrderSn();
        Integer userId = record.getUserId();
        Integer channelId = record.getChannelId();
        //落库：结果+处理状态
        PlusOrderResubmitFlagEntity saveRes = new PlusOrderResubmitFlagEntity();
        saveRes.setId(record.getId());
        try {
            log.info("订单重提标识查询，orderSn：{}，userId：{}", orderSn, userId);
            JSONArray userLabel = engineExternalRepository.enginePreprocess(null, userId,
                    EngineCodeConstant.RE_SUBMIT_CODE, channelId);
            //查询重提标识异常或超时,需要重试
            if (ObjectUtils.isEmpty(userLabel)) {
                log.info("订单重提标识查询-桔测返回data为空，orderSn：{}，userId：{}", orderSn, userId);
                throw new PlusAbyssException("桔测返回data为空");
            }
            JSONArray channelResult = userLabel.getJSONObject(0).getJSONArray("channelResult");
            if (ObjectUtils.isEmpty(channelResult)) {
                log.info("订单重提标识查询-channelResult为空，orderSn：{}，userId：{}", orderSn,
                        userId);
                throw new PlusAbyssException("桔channelResult为空");
            }
            //判断是否有重提标签
            saveRes.setVerifyResult("非标签用户");
            if (channelResult.contains(EngineResultConstant.RE_SUBMIT_RES)) {
                saveRes.setVerifyResult("标签用户");
                saveRes.setResubmitFlag(CommonConstant.ONE);
            }
            //查证状态：2_查证成功
            saveRes.setVerifyStatus(CommonConstant.TWO);
            log.info("订单重提标识查询结束：记录信息:{},orderSn：{}", JSON.toJSONString(saveRes),
                    orderSn);
            orderRepository.updatePlusOrderResubmitFlag(saveRes);
        } catch (PlusAbyssException e) {
            log.info("订单重提标识查询-业务异常 orderSn：{}", orderSn, e);
            //调用未知异常
            buildVerifyRes(record, saveRes, e.getMessage());
            log.info("订单重提标识查询业务异常：记录信息:{},orderSn：{}", JSON.toJSONString(saveRes),
                    orderSn);
            orderRepository.updatePlusOrderResubmitFlag(saveRes);
        } catch (Exception e) {
            log.info("订单重提标识查询-未知异常 orderSn：{}", orderSn, e);
            //调用未知异常
            buildVerifyRes(record, saveRes, "查询未知异常");
            log.info("订单重提标识查询未知异常：记录信息:{},orderSn：{}", JSON.toJSONString(saveRes),
                    orderSn);
            orderRepository.updatePlusOrderResubmitFlag(saveRes);
        }
    }

    /**
     * 组装重提标识信息
     */
    private void buildVerifyRes(PlusOrderResubmitFlagEntity record,
            PlusOrderResubmitFlagEntity saveRes, String remark) {
        //失败原因
        saveRes.setVerifyResult(remark);
        //查证状态：3_查证失败
        saveRes.setVerifyStatus(CommonConstant.THREE);
        //计算重试次数：
        int retryCount = record.getRetryCount() + 1;
        //超过两次-存疑，不再执行
        if (retryCount >= CommonConstant.TWO) {
            //超过查询次数：4_存疑
            saveRes.setVerifyStatus(CommonConstant.FOUR);
        }
        saveRes.setRetryCount(retryCount);
    }


    @Override
    public boolean handleSignRdzxContract(PlusOrderEntity order, PlusRdzxRelationDetailEntity relationDetail) {
        boolean result = false;
        String plusOrderSn = order.getOrderSn();
        try {
            PlusOrderContractEvent plusOrderContractEvent = new PlusOrderContractEvent();
            plusOrderContractEvent.setCustomerId(order.getUserId());
            plusOrderContractEvent.setChannelId(order.getChannelId());
            plusOrderContractEvent.setLoanOrderId(relationDetail.getOrderSn());
            plusOrderContractEvent.setOrderId(plusOrderSn);
            plusOrderContractEvent.setLoanOrderAmount(relationDetail.getLoanAmount());
            log.info("=======补偿签署融单咨询卡订单合同入参=======plusOrderContractEvent#{}",JSON.toJSONString(plusOrderContractEvent));
            // 给合同系统发消息时，dateMap和htxt_sign_time两个时间用订单创建的时间
            /**
             * 返回值的说明
             * -3 表示用户已注销，返回true不再处理
             * -2表示根据订单号没有查询到数据，返回true，不再处理
             * -1表示合同编号为空，返回false，需要再次处理
             * 0 表示发送消息失败，返回false，需要再次处理
             * 1 表示消息发送成功，返回true，不再处理
             */
            Integer resultNum = plusOrderContractRepository.makeUpForSignOpenCardContractRdzx(
                    plusOrderContractEvent);
            if(Objects.equals(-3,resultNum) || Objects.equals(-2,resultNum)){
                log.info("=====补偿签署融单咨询卡订单合同,用户已注销或者根据订单号没有查询到数据，不再处理========plusOrderSn#{}",plusOrderSn);
                result = true;
            }
            if(Objects.equals(1,resultNum)){
                result = true;
            }
            if(Objects.equals(0,resultNum)){
                log.info("=====补偿签署融单咨询卡订单合同,发送消息失败,需要再次处理====plusOrderSn#{}",plusOrderSn);
            }
            try {
                Thread.sleep(60L);
            } catch (InterruptedException e) {
                log.info("=======发送合同消息,等待过程出现了异常======plusOrderSn#{}",plusOrderSn);
            }
        } catch (Exception e) {
            log.info("======补偿签署融单咨询卡订单合同出现异常=======id#{}，plusOrderSn#{}",order.getOrderId(),plusOrderSn,e);
        }
        return result;
    }
}


