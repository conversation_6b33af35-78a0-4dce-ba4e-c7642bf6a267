package com.juzifenqi.plus.module.order.model.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.core.date.LocalDateTimeUtils;
import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PayChannelEnum;
import com.juzifenqi.plus.enums.PayStatusEnum;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.RefundTypeEnum;
import com.juzifenqi.plus.enums.SupplierEnum;
import com.juzifenqi.plus.enums.pay.DefrayStateEnum;
import com.juzifenqi.plus.enums.pay.PayChannelV2Enum;
import com.juzifenqi.plus.enums.pay.PayProductCodeEnum;
import com.juzifenqi.plus.enums.refund.RefundStateEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusInfoDetailRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusInfoRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusOrderInfoRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ExpireMemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusOrderInfoEntity;
import com.juzifenqi.plus.module.asserts.model.event.QueryProfitUsedEvent;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.profits.ProfitHandlerContext;
import com.juzifenqi.plus.module.common.IDecryptRepository;
import com.juzifenqi.plus.module.common.IFmsRepository;
import com.juzifenqi.plus.module.common.IMemberExternalRepository;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.common.entity.DefrayQueryV2RespEntity;
import com.juzifenqi.plus.module.common.entity.PayRecordResultEntity;
import com.juzifenqi.plus.module.common.entity.RefundRespEntity;
import com.juzifenqi.plus.module.common.entity.TraderInfoRespEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.plus.module.order.adapter.IPlusOrderAdapter;
import com.juzifenqi.plus.module.order.model.PlusOrderDetailModel;
import com.juzifenqi.plus.module.order.model.PlusOrderSnapshtoQueryModel;
import com.juzifenqi.plus.module.order.model.contract.*;
import com.juzifenqi.plus.module.order.model.contract.entity.OrderPayResultEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PastMemberRefundRecordEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusCancelByRatioRecordEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailMemberEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailProfitEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailRefundEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.ShuntEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.bill.PlusOrderBillEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IAlphaExternalRepository;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderShuntPo;
import com.juzifenqi.plus.module.program.model.contract.IPlusProModelRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 订单详情领域model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/2 15:25
 */
@Service
@Slf4j
public class PlusOrderDetailModelImpl implements PlusOrderDetailModel {

    @Autowired
    private IFmsRepository                    fmsRepository;
    @Autowired
    private IDecryptRepository                decryptRepository;
    @Autowired
    private IMemberPlusOrderInfoRepository    memberPlusOrderInfoRepository;
    @Autowired
    private IAlphaExternalRepository          alphaExternalRepository;
    @Autowired
    private IPlusCancelByRatioRepository      cancelByRatioRepository;
    @Autowired
    private IPlusOrderRepository              orderRepository;
    @Autowired
    private IPlusOrderShuntRepository         shuntRepository;
    @Autowired
    private IPlusOrderBillRepository          orderBillRepository;
    @Autowired
    private IPastMemberRefundRecordRepository refundRecordRepository;
    @Autowired
    private IMemberExternalRepository         memberExternalRepository;
    @Autowired
    private IMemberPlusInfoRepository         memberPlusInfoRepository;
    @Autowired
    private IMemberPlusInfoDetailRepository   plusInfoDetailRepository;
    @Autowired
    private IPlusProModelRepository           plusProModelRepository;
    @Autowired
    private IMemberPlusInfoDetailRepository   memberPlusInfoDetailRepository;
    @Autowired
    private ProfitHandlerContext              profitHandlerContext;
    @Autowired
    private IPlusShuntRepository        plusShuntRepository;
    @Autowired
    private PlusOrderSnapshtoQueryModel plusOrderSnapshtoQueryModel;
    @Autowired
    private IPlusOrderAdapter                 plusOrderAdapter;
    @Autowired
    private IPlusOrderRefundInfoRepository    plusOrderRefundInfoRepository;
    @Autowired
    private IPlusOrderSeparateRepository plusOrderSeparateRepository;

    /**
     * 获取订单信息
     */
    @Override
    public PlusOrderDetailOrderEntity getOrderInfo(PlusOrderEntity orderEntity) {
        String orderSn = orderEntity.getOrderSn();
        log.info("订单详情-设置订单信息,开始,订单号{}", orderSn);
        PlusOrderDetailOrderEntity detailEntity = new PlusOrderDetailOrderEntity();
        detailEntity.setOrderSn(orderSn);
        detailEntity.setOrderAmount(orderEntity.getOrderAmount());
        detailEntity.setPayAmount(orderEntity.getPayAmount());
        detailEntity.setDiscountRate(orderEntity.getDiscountRate());
        detailEntity.setConfigId(orderEntity.getConfigId());
        // 默认待支付
        //detailEntity.setPayState(PayStatusEnum.PAY_STATUS_0.getCode());
        //TODO 支付方式获取废弃
        /** try {
            //获取支付状态
           OrderPayResultEntity orderPayResultEntity = plusOrderAdapter.queryPayRecord(orderSn);
            if (orderPayResultEntity != null) {
                TraderInfoRespEntity traderInfoRespEntity = orderPayResultEntity.getTraderInfoRespEntity();
                detailEntity.setPayState(
                        PayStatusEnum.getCodeByPay(traderInfoRespEntity.getState()));
                PayProductCodeEnum payProductCodeEnum = PayProductCodeEnum.getTypeByCode(
                        traderInfoRespEntity.getPayProductCode());
                if (payProductCodeEnum != null) {
                    detailEntity.setPayType(payProductCodeEnum.getDesc());
                }
                String ctCardNo = traderInfoRespEntity.getCtCardNo();
                if (StringUtils.isNotBlank(ctCardNo)) {
                    String cardNo = decryptRepository.decrypt(ctCardNo,
                            "浩瀚会员订单详情解密支付银行卡号");
                    detailEntity.setPayCardNo(cardNo.substring(cardNo.length() - CommonConstant.FOUR));
                }
            } else {
                detailEntity.setPayState(convertPayState(orderEntity));
            }
        } catch (Exception e) {
            // 20240131 zjf 查询支付结果异常就获取订单自己的状态，映射给前端
            detailEntity.setPayState(convertPayState(orderEntity));
            log.info("订单详情-設置支付方式异常,订单号{}", orderSn, e);
        }**/
        // 增加是否提额的展示信息
        MemberPlusOrderInfoEntity infoEntity = memberPlusOrderInfoRepository.getInfoByOrderNo(
                orderSn);
        log.info("订单详情-提额的展示信息,订单号{},info {}", orderSn,
                JSON.toJSONString(infoEntity));
        Optional.ofNullable(infoEntity).ifPresent(e -> {
            detailEntity.setQuotaInfo(e.getMsgId());
            detailEntity.setSendQuotaInfo(true);
        });
        //设置是否可取消
        if (orderEntity.getOrderState() == PlusOrderStateEnum.PAY_SUCCESS.getCode() || (
                orderEntity.getOrderState() == PlusOrderStateEnum.WAIT_PAY.getCode()
                        && Objects.equals(orderEntity.getPayType(),
                        PlusOrderPayTypeEnum.PAY_AFTER.getValue()))) {
            // 检查会员能否取消
            PlusOrderCancelEvent event = new PlusOrderCancelEvent();
            event.setChannelId(orderEntity.getChannelId());
            event.setConfigId(orderEntity.getConfigId());
            event.setUserId(orderEntity.getUserId());
            event.setPlusOrderSn(orderEntity.getOrderSn());
            event.setProgramId(orderEntity.getProgramId());
            detailEntity.setCanBeCancel(false);
            try {
                Integer cancelSense = PlusCancelTypeEnum.CONDITION.getCancelSense();
                if (cancelSense == null) {
                    throw new PlusAbyssException("未配置取消场景code");
                }
                // 校验取消规则
                alphaExternalRepository.cancelPreCheck(event, cancelSense);
                detailEntity.setCanBeCancel(true);
            } catch (Exception e) {
                log.info("订单详情-校验订单不可取消,订单号{}", orderSn);
            }
        }
        log.info("订单详情-设置订单信息结束,订单号{}", orderSn);
        return detailEntity;
    }

    @Override
    public List<PlusOrderSeparateEntity> getOrderSeparateInfo(PlusOrderEntity orderEntity) {
        String orderSn = orderEntity.getOrderSn();
        log.info("订单详情-设置订单分账记录信息BEGIN,订单号{}", orderSn);
        List<PlusOrderSeparateEntity> orderSeparateByOrderNo =
                plusOrderSeparateRepository.getOrderSeparateByOrderNo(orderSn);
        log.info("订单详情-设置订单分账记录信息END,订单号{}", orderSn);
        return orderSeparateByOrderNo;
    }

    /**
     * 映射会员订单状态->支付状态
     */
    private Integer convertPayState(PlusOrderEntity order) {
        Integer orderState = order.getOrderState();
        if (orderState == PlusOrderStateEnum.WAIT_PAY.getCode()) {
            return PayStatusEnum.PAY_STATUS_0.getCode();
        }
        if (orderState == PlusOrderStateEnum.PAY_SUCCESS.getCode()) {
            return PayStatusEnum.PAY_STATUS_2.getCode();
        }
        // 订单支付过且已取消=退款，未支付且已取消=待支付
        if (orderState == PlusOrderStateEnum.CANCELED.getCode()) {
            return order.getPayTime() != null ? PayStatusEnum.PAY_STATUS_4.getCode()
                    : PayStatusEnum.PAY_STATUS_0.getCode();
        }
        return PayStatusEnum.PAY_STATUS_0.getCode();
    }

    /**
     * 获取退款信息
     */
    @Override
    public PlusOrderDetailRefundEntity getRefundInfo(PlusOrderEntity orderEntity) {
        String orderSn = orderEntity.getOrderSn();
        log.info("订单详情-设置退款信息开始,订单号{}", orderSn);
        if (orderEntity.getOrderState() == PlusOrderStateEnum.WAIT_PAY.getCode()) {
            log.info("订单详情-待支付订单-不设置退款信息,订单号{}", orderSn);
            return null;
        }
        PlusOrderDetailRefundEntity refundEntity = new PlusOrderDetailRefundEntity();
        // 设置部分扣减详情
        PlusOrderCancelEvent cancelEvent = new PlusOrderCancelEvent();
        cancelEvent.setPlusOrderSn(orderEntity.getOrderSn());
        cancelEvent.setCancelType(PlusCancelTypeEnum.CONDITION.getValue());
        cancelEvent.setUserId(orderEntity.getUserId());
        PlusOrderCancelEntity cancelEntity = orderRepository.sumDeductPrice(cancelEvent,
                orderEntity);
        refundEntity.setNeedDeductPrice(cancelEntity.isNeedDeductPrice());
        refundEntity.setProfitNum(cancelEntity.getProfitNum());
        refundEntity.setDeductPrice(cancelEntity.getDeductPrice());
        refundEntity.setRefundAmount(orderEntity.getRefundAmount());
        // 设置比例退款详情
        PlusCancelByRatioRecordEntity cancelByRatioRecordEntity = cancelByRatioRepository.selectByOrderSn(
                orderSn);
        if (cancelByRatioRecordEntity != null) {
            refundEntity.setRefundRatio(cancelByRatioRecordEntity.getRefundRatio());
        }
        // 设置入账、出账信息
        ShuntEntity shuntEntity = getShunt(orderSn);
        if (shuntEntity != null) {
            refundEntity.setOutSupplier(shuntEntity.getOutSupplier());
            refundEntity.setPlanInSupplier(shuntEntity.getPlanInSupplier());
            refundEntity.setInSupplier(shuntEntity.getInSupplier());
        }
        // 设置退款详情
        try {
            // 是否是新支付系统数据
            PlusOrderRefundInfoEntity refundInfoEntity = plusOrderRefundInfoRepository.getLastOneByOrderSn(
                    orderSn);
            if (refundInfoEntity != null) {
                if (Objects.equals(refundInfoEntity.getRefundType(),
                        RefundTypeEnum.CDF.getCode())) {
                    // 纯代付
                    DefrayQueryV2RespEntity defrayRespEntity = fmsRepository.queryDefrayV2(
                            refundInfoEntity.getRefundSerialNo(), null);
                    supplyDefrayResult(refundEntity, defrayRespEntity,
                            refundInfoEntity.getRefundRate());
                    log.info("订单详情-新退款-纯代付-设置退款信息结束,订单号{}", orderSn);
                } else {
                    // 原路退
                    RefundRespEntity refundRespEntity = fmsRepository.queryRefundResultV2(
                            refundInfoEntity.getRefundSerialNo(), null);
                    supplyRefundResult(refundEntity, refundRespEntity,
                            refundInfoEntity.getRefundType());
                    log.info("订单详情-新退款-原路退-设置退款信息结束,订单号{} 退款类型{}", orderSn,
                            refundInfoEntity.getRefundType());
                }
                return refundEntity;
            }
            // 是否是纯代付
            PastMemberRefundRecordEntity refundRecordEntity = refundRecordRepository.getByOrderSn(
                    orderSn);
            if (refundRecordEntity != null) {
                // 纯代付
                DefrayQueryV2RespEntity defrayRespEntity = fmsRepository.queryDefrayV2(null,
                        refundRecordEntity.getSerialNumber());
                supplyDefrayResult(refundEntity, defrayRespEntity,
                        refundRecordEntity.getRefundRatio());
                log.info("订单详情-历史订单-代付-设置退款信息结束,订单号{}", orderSn);
            } else {
                // 原路退
                RefundRespEntity refundRespEntity = fmsRepository.queryRefundResultV2(orderSn);
                supplyRefundResult(refundEntity, refundRespEntity, RefundTypeEnum.YLT.getCode());
                log.info("订单详情-历史订单-原路退-设置退款信息结束,订单号{}", orderSn);
            }
        } catch (Exception e) {
            log.info("订单详情-设置退款信息异常,订单号{}", orderSn, e);
        }
        return refundEntity;
    }

    /**
     * 退款信息列表
     * @param orderEntity
     * @return
     */
    @Override
    public List<PlusOrderDetailRefundEntity>  getRefundList(PlusOrderEntity orderEntity) {

        String orderSn = orderEntity.getOrderSn();
        log.info("订单详情-设置退款信息开始,订单号{}", orderSn);
        if (orderEntity.getOrderState() == PlusOrderStateEnum.WAIT_PAY.getCode()) {
            log.info("订单详情-待支付订单-不设置退款信息,订单号{}", orderSn);
            return null;
        }

        PlusOrderDetailRefundEntity refundEntity = new PlusOrderDetailRefundEntity();
        // 设置比例退款详情
        PlusCancelByRatioRecordEntity cancelByRatioRecordEntity = cancelByRatioRepository.selectByOrderSn(
                orderSn);
        if (cancelByRatioRecordEntity != null) {
            refundEntity.setRefundRatio(cancelByRatioRecordEntity.getRefundRatio());
        }
        // 设置入账、出账信息
        ShuntEntity shuntEntity = getShunt(orderSn);
        if (shuntEntity != null) {
            refundEntity.setOutSupplier(shuntEntity.getOutSupplier());
            refundEntity.setPlanInSupplier(shuntEntity.getPlanInSupplier());
            refundEntity.setInSupplier(shuntEntity.getInSupplier());
        }
        // 设置退款详情
        try {
            // 是否是新支付系统数据
            PlusOrderRefundInfoEntity refundInfoEntity = plusOrderRefundInfoRepository.getLastOneByOrderSn(
                    orderSn);
            if (refundInfoEntity != null) {
                //查询退款明细
                if (Objects.equals(refundInfoEntity.getRefundType(),
                        RefundTypeEnum.CDF.getCode())) {
                    // 纯代付
                    DefrayQueryV2RespEntity defrayRespEntity = fmsRepository.queryDefrayV2(
                            refundInfoEntity.getRefundSerialNo(), null);
                    supplyDefrayResult(refundEntity, defrayRespEntity,
                            refundInfoEntity.getRefundRate());
                    log.info("订单详情-新退款-纯代付-设置退款信息结束,订单号{}", orderSn);
                } else {
                    // 原路退
                    RefundRespEntity refundRespEntity = fmsRepository.queryRefundResultV2(
                            refundInfoEntity.getRefundSerialNo(), null);
                    supplyRefundResult(refundEntity, refundRespEntity,
                            refundInfoEntity.getRefundType());
                    log.info("订单详情-新退款-原路退-设置退款信息结束,订单号{} 退款类型{}", orderSn,
                            refundInfoEntity.getRefundType());
                }
                return null;
            }
            // 是否是纯代付
            PastMemberRefundRecordEntity refundRecordEntity = refundRecordRepository.getByOrderSn(
                    orderSn);
            if (refundRecordEntity != null) {
                // 纯代付
                DefrayQueryV2RespEntity defrayRespEntity = fmsRepository.queryDefrayV2(null,
                        refundRecordEntity.getSerialNumber());
                supplyDefrayResult(refundEntity, defrayRespEntity,
                        refundRecordEntity.getRefundRatio());
                log.info("订单详情-历史订单-代付-设置退款信息结束,订单号{}", orderSn);
            } else {
                // 原路退
                RefundRespEntity refundRespEntity = fmsRepository.queryRefundResultV2(orderSn);
                supplyRefundResult(refundEntity, refundRespEntity, RefundTypeEnum.YLT.getCode());
                log.info("订单详情-历史订单-原路退-设置退款信息结束,订单号{}", orderSn);
            }
        } catch (Exception e) {
            log.info("订单详情-设置退款信息异常,订单号{}", orderSn, e);
        }
        return null;
    }
    /**
     * 补充退款结果
     *
     * @param refundEntity 退款返回详情实体
     * @param refundRespEntity 支付返回退款结果
     */
    private void supplyRefundResult(PlusOrderDetailRefundEntity refundEntity,
            RefundRespEntity refundRespEntity, Integer refundType) {
        if ("100027".equals(refundRespEntity.getErrorCode())) {
            // hxf 2024.9.25 支付侧返回100027表示无退款记录，因为无记录，支付也会正常返回状态，所以会员侧特殊判断，无记录则不做状态判断和赋值
            return;
        }
        // hxf 2024.9.20 如果是换卡代付，虽然走的原路退的接口，但是只是关联了原交易，实际还是代付业务，所以退款类型需要展示代付，并且展示银行和卡号
        refundEntity.setRefundType(
                RefundTypeEnum.YLT.getCode().equals(refundType) ? CommonConstant.ZERO
                        : CommonConstant.ONE);
        // 20231011 zjf 发起过退款才处理退款状态、退款通道、退款失败原因
        if (StringUtils.isNotBlank(refundRespEntity.getStatus())) {
            refundEntity.setRefundState(RefundStateEnum.getCodeByPay(refundRespEntity.getStatus()));
            refundEntity.setRefundChannel(
                    PayChannelV2Enum.getRealNameByCode(refundRespEntity.getPayChannel()));
            refundEntity.setRefundFailMsg(refundRespEntity.getErrorMsg());
        }
        String ctCardNo = refundRespEntity.getCtCardNo();
        if (!StringUtils.isEmpty(ctCardNo)) {
            String cardNo = decryptRepository.decrypt(ctCardNo,
                    "浩瀚会员订单详情解密原路退款银行卡号");
            refundEntity.setRefundBankCardNo(
                    cardNo.substring(cardNo.length() - CommonConstant.FOUR));
        }
        refundEntity.setRefundSerialNumber(refundRespEntity.getPaySerialNum());
        refundEntity.setRefundBankName(refundRespEntity.getBankName());
    }

    /**
     * 补充代付结果
     *
     * @param refundEntity 退款返回详情实体
     * @param defrayRespEntity 支付返回代付结果
     * @param refundRate 退款比例
     */
    private void supplyDefrayResult(PlusOrderDetailRefundEntity refundEntity,
            DefrayQueryV2RespEntity defrayRespEntity, BigDecimal refundRate) {
        refundEntity.setRefundRatio(refundRate);
        refundEntity.setRefundType(CommonConstant.ONE);
        refundEntity.setRefundState(DefrayStateEnum.getCodeByState(defrayRespEntity.getStatus()));
        refundEntity.setRefundFailMsg(defrayRespEntity.getErrorMsg());
        refundEntity.setRefundSerialNumber(defrayRespEntity.getPaySerialNum());
        refundEntity.setRefundBankName(defrayRespEntity.getBankName());
        if (!StringUtils.isEmpty(defrayRespEntity.getCtCardNo())) {
            String cardNo = decryptRepository.decrypt(defrayRespEntity.getCtCardNo(),
                    "浩瀚会员订单详情解密代付退款银行卡号");
            refundEntity.setRefundBankCardNo(
                    cardNo.substring(cardNo.length() - CommonConstant.FOUR));
        }
    }

    /**
     * 获取分流信息
     */
    private ShuntEntity getShunt(String orderSn) {
        log.info("获取分流信息：{}", orderSn);
        PlusOrderShuntPo plusOrderShuntEntity = shuntRepository.getByOrderSn(orderSn);
        // 如果计划入账方都没有，那么实际入账方和出账方也没有
        if (plusOrderShuntEntity == null) {
            return null;
        }
        ShuntEntity shuntEntity = new ShuntEntity();
        // 计划入账方
        if (plusOrderShuntEntity.getPlanInSupplier() != null) {
            PlusShuntSupplierEntity supplierCache = plusShuntRepository.getSupplierCache(
                    plusOrderShuntEntity.getPlanInSupplier());
            if (supplierCache != null) {
                shuntEntity.setPlanInSupplier(supplierCache.getSupplierName());
            } else {
                // 兼容上线期间，分流主体未配置的情况
                shuntEntity.setPlanInSupplier(
                        SupplierEnum.getName(plusOrderShuntEntity.getPlanInSupplier()));
            }
        }
        // 实际入账方
        if (plusOrderShuntEntity.getInSupplier() != null) {
            PlusShuntSupplierEntity supplierCache = plusShuntRepository.getSupplierCache(
                    plusOrderShuntEntity.getInSupplier());
            if (supplierCache != null) {
                shuntEntity.setInSupplier(supplierCache.getSupplierName());
            } else {
                // 兼容上线期间，分流主体未配置的情况
                shuntEntity.setInSupplier(
                        SupplierEnum.getName(plusOrderShuntEntity.getInSupplier()));
            }
        }
        // 出账方
        PlusOrderBillEntity orderBillEntity = orderBillRepository.getByOrderSn(orderSn);
        if (orderBillEntity != null && orderBillEntity.getOutSupplier() != null) {
            PlusShuntSupplierEntity supplierCache = plusShuntRepository.getSupplierCache(
                    orderBillEntity.getOutSupplier());
            if (supplierCache != null) {
                shuntEntity.setOutSupplier(supplierCache.getSupplierName());
            } else {
                // 兼容上线期间，分流主体未配置的情况
                shuntEntity.setOutSupplier(SupplierEnum.getName(orderBillEntity.getOutSupplier()));
            }
        }
        return shuntEntity;
    }

    /**
     * 获取用户信息
     */
    @Override
    public PlusOrderDetailMemberEntity getMemberInfo(PlusOrderEntity orderEntity) {
        String orderSn = orderEntity.getOrderSn();
        log.info("订单详情-设置用户信息开始,订单号{}", orderSn);
        PlusOrderDetailMemberEntity entity = new PlusOrderDetailMemberEntity();
        Integer userId = orderEntity.getUserId();
        Integer channelId = orderEntity.getChannelId();
        Integer configId = orderEntity.getConfigId();
        MemberInfo member = memberExternalRepository.getById(userId);
        String mobile = decryptRepository.decrypt(member.getMobileDes(),
                "浩瀚后台-会员用户管理-查看详情解密手机号");
        entity.setUserId(userId);
        entity.setUserName(member.getRealName());
        entity.setMobile(mobile);
        entity.setChannelId(channelId);
        //获取总开始时间，结束时间
        setAllPeriodsDate(entity, userId, channelId, configId);
        //设置所有会员类型
        setAllConfigType(entity, userId, channelId);
        log.info("订单详情-设置用户信息结束,订单号{}", orderSn);
        return entity;
    }

    /**
     * 设置总开始时间，结束时间
     */
    private void setAllPeriodsDate(PlusOrderDetailMemberEntity memberEntity, Integer userId,
            Integer channelId, Integer configId) {
        log.info("订单详情，设置总结束时间开始：userId:{},configId:{}", userId, configId);
        if (!PlusConstant.MERGE_CARD_LIST.contains(configId)) {
            return;
        }
        //查询用户当期的所有会员，按会员订单创建时间倒序（桔享卡、加速卡、固额卡），获取总开始时间，结束时间
        List<MemberPlusInfoDetailEntity> detailEntities = memberPlusInfoRepository.getCurrentAllMemberInfo(
                userId, channelId, PlusConstant.MERGE_CARD_LIST);
        log.info("订单详情，获取当期的所有会员信息开始：userId:{},infos:{}", userId,
                JSON.toJSONString(detailEntities));
        if (CollectionUtils.isEmpty(detailEntities)) {
            return;
        }
        //最晚到期时间
        AtomicReference<Date> endTime = new AtomicReference<>(new Date());
        //最早
        AtomicReference<Date> startTime = new AtomicReference<>(new Date());
        detailEntities.forEach(detailInfo -> {
            //设置最晚到期时间
            Date jxEndTime = detailInfo.getJxEndTime();
            if (jxEndTime.after(endTime.get())) {
                endTime.set(jxEndTime);
            }
            //设置最早开始时间
            Date jxStartTime = detailInfo.getJxStartTime();
            if (jxStartTime.before(startTime.get())) {
                startTime.set(jxStartTime);
            }
        });
        memberEntity.setEndTime(LocalDateTimeUtils.parseDateToString(endTime.get(),
                LocalDateTimeUtils.DATE_FORMAT_FULL));
        memberEntity.setStartTime(LocalDateTimeUtils.parseDateToString(startTime.get(),
                LocalDateTimeUtils.DATE_FORMAT_FULL));
        log.info("订单详情，设置结束时间结束：userId:{},infos:{}", userId,
                JSON.toJSONString(memberEntity));
    }

    /**
     * 设置所有会员类型
     */
    private void setAllConfigType(PlusOrderDetailMemberEntity memberEntity, Integer userId,
            Integer channelId) {
        log.info("订单详情，设置所有会员类型开始：userId:{}", userId);
        //查询用户当期的所有会员，按会员订单创建时间倒序（桔享卡、加速卡、固额卡），获取总开始时间，结束时间
        List<MemberPlusInfoDetailEntity> detailEntities = memberPlusInfoRepository.getMemberPlusListByUserId(
                userId, channelId);
        log.info("订单详情，设置所有会员类型开始：userId:{},infos:{}", userId,
                JSON.toJSONString(detailEntities));
        if (CollectionUtils.isEmpty(detailEntities)) {
            return;
        }
        String allType = detailEntities.stream().map(MemberPlusInfoDetailEntity::getConfigId)
                .distinct().map(JuziPlusEnum::getNameByCode).collect(Collectors.joining(","));
        log.info("订单详情，设置所有会员类型结束：userId:{},allType:{}", userId, allType);
        memberEntity.setAllPlusType(allType);
    }

    /**
     * 获取权益信息
     */
    @Override
    public PlusOrderDetailProfitEntity getProfitInfo(PlusOrderEntity orderEntity) {
        String orderSn = orderEntity.getOrderSn();
        log.info("订单详情-设置权益信息开始,订单号{}", orderSn);
        //判断该会员是否过期：
        Date endTime = orderEntity.getEndTime();
        if (endTime == null) {
            return null;
        }
        PlusOrderDetailProfitEntity entity = new PlusOrderDetailProfitEntity();
        if (endTime.before(new Date())
                || orderEntity.getOrderState() == PlusOrderStateEnum.CANCELED.getCode()) {
            // 过期会员权益设置
            pastPlusInfoSet(orderEntity, entity);
            return entity;
        }
        // 未过期
        MemberPlusInfoDetailEntity detailEntity = memberPlusInfoDetailRepository.getByOrderSn(
                orderSn);
        log.info("订单详情-设置权益信息-获取会员信息,订单号{},info:{}", orderSn,
                JSON.toJSONString(detailEntity));
        if (detailEntity == null) {
            return entity;
        }
        entity.setDetailId(detailEntity.getId());
        entity.setPlusState(detailEntity.getJxStatus());
        int daysBetween = LocalDateTimeUtils.getDaysBetween(detailEntity.getJxStartTime(),
                new Date());
        entity.setEffectiveNum(Math.max(daysBetween, CommonConstant.ZERO));
        entity.setCurrentStartTime(
                LocalDateTimeUtils.formatDateToStr(detailEntity.getJxStartTime()));
        entity.setCurrentEndTime(LocalDateTimeUtils.formatDateToStr(detailEntity.getJxEndTime()));
        //根据方案id查询关联的权益信息，按照顺序排列
        List<PlusProModelEntity> rights = plusOrderSnapshtoQueryModel.listOrderModelByOrderBySort(orderEntity);
        Map<Integer, Object> profitMap = new HashMap<>();
        rights.forEach(proModel -> {
            QueryProfitUsedEvent event = new QueryProfitUsedEvent();
            event.setOrderSn(detailEntity.getOrderSn());
            event.setProgramId(detailEntity.getProgramId());
            event.setChannel(detailEntity.getChannelId());
            event.setUserId(detailEntity.getUserId());
            event.setConfigId(detailEntity.getConfigId());
            event.setJxStartTime(detailEntity.getJxStartTime());
            event.setJxEndTime(detailEntity.getJxEndTime());
            event.setModelId(proModel.getModelId());
            Map<String, Object> dataMap = profitHandlerContext.getProfitUsedInfo(event);
            profitMap.put(proModel.getModelId(), dataMap);
        });
        entity.setProfitList(profitMap);
        log.info("订单详情-设置权益信息结束,订单号{}", orderSn);
        return entity;
    }

    /**
     * 过期会员权益设置
     */
    private void pastPlusInfoSet(PlusOrderEntity orderEntity, PlusOrderDetailProfitEntity entity) {
        String orderSn = orderEntity.getOrderSn();
        Integer userId = orderEntity.getUserId();
        log.info("订单详情-过期会员权益设置开始,订单号{}", orderSn);
        // 获取失效会员信息
        ExpireMemberPlusInfoDetailEntity plusInfoDetailEntity = plusInfoDetailRepository.getExpireMemberPlusInfoDetail(
                userId, orderSn);
        log.info("订单详情-获取失效会员信息返回结果={}", JSONObject.toJSON(plusInfoDetailEntity));
        if (plusInfoDetailEntity == null) {
            return;
        }
        entity.setDetailId(plusInfoDetailEntity.getId());
        entity.setPlusState(plusInfoDetailEntity.getJxStatus());
        int daysBetween = LocalDateTimeUtils.getDaysBetween(plusInfoDetailEntity.getJxStartTime(),
                new Date());
        entity.setEffectiveNum(Math.max(daysBetween, CommonConstant.ZERO));
        entity.setCurrentStartTime(
                LocalDateTimeUtils.formatDateToStr(plusInfoDetailEntity.getJxStartTime()));
        entity.setCurrentEndTime(
                LocalDateTimeUtils.formatDateToStr(plusInfoDetailEntity.getJxEndTime()));
        // 根据方案id查询关联的权益信息，按照顺序排列
        List<PlusProModelEntity> rights = plusProModelRepository.getProModelByProgramIdOrderBySort(
                orderEntity.getProgramId());
        Map<Integer, Object> profitMap = new HashMap<>();
        rights.forEach(proModel -> {
            QueryProfitUsedEvent event = new QueryProfitUsedEvent();
            event.setOrderSn(plusInfoDetailEntity.getOrderSn());
            event.setProgramId(plusInfoDetailEntity.getProgramId());
            event.setChannel(plusInfoDetailEntity.getChannelId());
            event.setUserId(plusInfoDetailEntity.getUserId());
            event.setConfigId(plusInfoDetailEntity.getConfigId());
            event.setModelId(proModel.getModelId());
            Map<String, Object> dataMap = profitHandlerContext.getProfitUsedInfoExpire(event);
            profitMap.put(proModel.getModelId(), dataMap);
        });
        entity.setProfitList(profitMap);
        log.info("订单详情-过期会员权益设置结束,订单号{}", orderSn);
    }
}
