package com.juzifenqi.plus.module.order.repository.impl;

import com.juzifenqi.plus.module.order.model.contract.IPlusPayRecordRepository;
import com.juzifenqi.plus.module.order.repository.dao.IPlusPayRecordMapper;
import com.juzifenqi.plus.module.order.repository.po.PlusPayRecordPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 支付记录
 * <p>目前是对外输出888渠道使用</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/17 13:56
 */
@Slf4j
@Repository
public class PlusPayRecordRepositoryImpl implements IPlusPayRecordRepository {

    @Autowired
    private IPlusPayRecordMapper plusPayRecordMapper;

    @Override
    public PlusPayRecordPo getByOrderSn(String orderSn) {
        return plusPayRecordMapper.loadPlusPayRecord(orderSn);
    }

    @Override
    public void update(PlusPayRecordPo recordPo) {
        plusPayRecordMapper.updatePlusPayRecord(recordPo);
    }
}
