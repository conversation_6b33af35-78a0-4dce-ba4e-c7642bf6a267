package com.juzifenqi.plus.module.common.repository.external.acl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.pojo.CreateOrderContext;
import com.juzifenqi.plus.enums.BusinessTypeEnum;
import com.juzifenqi.plus.enums.CreateOrderFlagEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.OrderRenewEnum;
import com.juzifenqi.plus.enums.discount.DiscountConditionFieldEnum;
import com.juzifenqi.plus.enums.discount.DiscountConditionKeyEnum;
import com.juzifenqi.plus.enums.discount.DiscountConfTypeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.IEngineExternalRepository;
import com.juzifenqi.plus.module.common.IPlusOrderExternalRepository;
import com.juzifenqi.plus.module.common.IProductExternalRepository;
import com.juzifenqi.plus.module.common.IUserSignExternalRepository;
import com.juzifenqi.plus.module.common.entity.DistributionMarketEntity;
import com.juzifenqi.plus.module.common.entity.UserSignInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.IDiscountRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusDiscountEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRdzxServiceFeeEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IRdzxExternalRepository;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusProductOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualGoodsOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualOrderCreateEvent;
import com.juzifenqi.plus.module.order.repository.po.MemberPlusDiscountConfPo;
import com.juzifenqi.plus.module.order.repository.po.MemberPlusDiscountRecordPo;
import com.juzifenqi.plus.module.order.repository.po.PlusDiscountConditionPo;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramLmkVirtualEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo;
import com.juzifenqi.plus.utils.PlusConcatUtils;
import com.juzifenqi.plus.utils.RedisUtils;
import com.juzifenqi.product.entity.Product;
import com.juzifenqi.product.search.vo.ProductDetailCacheVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 获取折扣实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/28 11:23
 */
@Service
@Slf4j
public class PlusOrderExternalRepositoryAcl implements IPlusOrderExternalRepository {

    @Autowired
    private RedisUtils                   redisUtils;
    @Autowired
    private IEngineExternalRepository    engineExternalRepository;
    @Autowired
    private IUserSignExternalRepository  userSignExternalRepository;
    @Autowired
    private IDiscountRepository          iDiscountRepository;
    @Autowired
    private IRdzxExternalRepository      rdzxExternalRepository;
    @Autowired
    private IProductExternalRepository   productExternalRepository;
    @Autowired
    private IPlusProgramQueryModel       programQueryModel;
    @Autowired
    private IPlusOrderRepository         orderRepository;
    @Autowired
    private ProductExternalRepositoryAcl productAcl;

    @Override
    public PlusDiscountEntity getDiscount(Integer userId, Integer channelId, Integer configId,
            Integer sceneCode) {
        // 20230520 zjf 增加桔会卡折扣逻辑
        if (configId != JuziPlusEnum.NEW_JUXP_CARD.getCode()
                && configId != JuziPlusEnum.EXPEDITE_CARD.getCode()
                && configId != JuziPlusEnum.QUOTA_CARD.getCode()
                && configId != JuziPlusEnum.YITONG_CARD.getCode()
                && configId != JuziPlusEnum.JH_CARD.getCode()) {
            return null;
        }
        // 20231025 zjf 商城创单，非确认借款页和信用支付完成页才走新折扣
        if (sceneCode != null && (sceneCode == 1 || sceneCode == 4)) {
            String redisKey = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.MEMBER_DISCOUNT,
                    userId, configId);
            String discountCache = redisUtils.get(redisKey);
            log.info("借款首页/落地页获取新会员折扣缓存开始：{}，{}", userId, discountCache);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(discountCache)) {
                MemberPlusDiscountRecordPo record = JSONObject.parseObject(discountCache,
                        MemberPlusDiscountRecordPo.class);
                log.info("借款首页/落地页获取新折扣缓存完成，返回：{}，{}", userId,
                        record.getDiscountRate());
                return buildNewDiscount(record);
            }
        }
        String redisKey = PlusConcatUtils.symbolBelowStr(
                RedisConstantPrefix.DISCOUNT_CONDITION_RESULT, userId, configId, channelId);
        String discountInfo = redisUtils.get(redisKey);
        if (!StringUtils.isEmpty(discountInfo)) {
            log.info("折扣信息redis返回 key:{},val:{}", redisKey, discountInfo);
            if (RedisConstantPrefix.ZERO.equals(discountInfo)) {
                return null;
            }
            //缓存符合：标签 + conf状态
            return doHandleDiscountInfo(userId, configId, channelId, redisKey, discountInfo);
        }
        //无缓存：校验 标签 + conf状态 + 时效
        return doHandleDiscountInfo(userId, configId, channelId, redisKey, null);
    }

    @Override
    public List<MemberPlusDiscountRecordPo> getDiscountRecord(Integer userId, Integer channelId,
            Integer configId, Integer confType) {
        return iDiscountRepository.getUserRecordList(userId, channelId, confType, configId);
    }

    @Override
    public BigDecimal calPlusOrderAmount(PlusOrderCreateEvent event,
            PlusProgramEntity programEntity, BigDecimal discount) {
        // 融单咨询卡
        if (programEntity.getConfigId().equals(JuziPlusEnum.RDZX_CARD.getCode())) {
            PlusRdzxServiceFeeEntity rdzxServiceFeeEntity = rdzxExternalRepository.getRdzxServiceFeeEntity(
                    event);
            return rdzxServiceFeeEntity.getServiceFee();
        }
        // 还款卡首单且选择连续包月
        if (programEntity.getConfigId().equals(JuziPlusEnum.REPAY_CARD.getCode()) && Objects.equals(
                OrderRenewEnum.RENEW.getCode(), event.getRenew()) && Objects.equals(
                CreateOrderFlagEnum.FIRST.getCode(), event.getCreateFlag())) {
            return calGeneralRepayRenewAmount(programEntity);
        }
        // 其他: 方案价格 * 折扣
        return calGeneralPlusOrderAmount(event, programEntity, discount);
    }

    @Override
    public BigDecimal calVirtualOrderAmount(VirtualOrderCreateEvent createEvent,
            BigDecimal discount) {
        log.info("计算虚拟权益订单金额入参：{}，折扣：{}", JSON.toJSONString(createEvent), discount);
        Product product = productAcl.getProductById(createEvent.getProductId());
        if (product == null) {
            throw new PlusAbyssException("获取商品信息失败,请重试");
        }
        if (product.getMallMobilePrice() == null) {
            throw new PlusAbyssException("商品金额为空,无法下单");
        }
        // 折扣为空走原价
        if (discount == null) {
            log.info("计算虚拟权益订单金额折扣为空，返回原价：{}，sku：{}，用户id：{}",
                    product.getMallMobilePrice(), createEvent.getProductSku(),
                    createEvent.getUserId());
            return product.getMallMobilePrice();
        }
        BigDecimal orderAmount = product.getMallMobilePrice().multiply(discount)
                .setScale(2, RoundingMode.DOWN);
        log.info("计算虚拟权益订单金额返回：{}，sku：{}，用户id：{}", orderAmount,
                createEvent.getProductSku(), createEvent.getUserId());
        return orderAmount;
    }

    /**
     * 计算虚拟商品权益订单金额
     */
    @Override
    public BigDecimal calVirtualGoodsOrderAmount(VirtualGoodsOrderCreateEvent createEvent,
            BigDecimal discount) {
        log.info("计算虚拟商品权益订单金额入参：{},会员折扣：{}", JSON.toJSONString(createEvent),
                discount);
        ProductDetailCacheVo product = productAcl.getCacheProductById(createEvent.getProductId());
        if (product == null) {
            throw new PlusAbyssException("获取商品信息失败,请重试");
        }
        BigDecimal malMobilePrice = product.getMalMobilePrice();
        if (malMobilePrice == null) {
            throw new PlusAbyssException("商品金额为空,无法下单");
        }
        // 折扣为空走原价
        if (discount == null) {
            log.info("计算虚拟商品权益订单金额折扣为空，返回原价：{}，productId：{}，用户id：{}",
                    malMobilePrice, createEvent.getProductId(), createEvent.getUserId());
            return malMobilePrice;
        }
        BigDecimal orderAmount = malMobilePrice.multiply(discount).setScale(2, RoundingMode.DOWN);
        log.info("计算虚拟商品权益订单金额返回：{}，productId：{}，用户id：{}", orderAmount,
                createEvent.getProductId(), createEvent.getUserId());
        return orderAmount;
    }

    /**
     * 计算还款卡首次开通续费金额
     */
    private BigDecimal calGeneralRepayRenewAmount(PlusProgramEntity program) {
        log.info("计算还款卡首次开通续费金额开始：{}", program.getProgramId());
        PlusRenewRelevancePo relevance = programQueryModel.getRenewRelevanceByProgramId(
                program.getProgramId());
        if (relevance == null || relevance.getRenewPrice() == null
                || relevance.getRenewPrice().compareTo(BigDecimal.ZERO) == 0) {
            throw new PlusAbyssException("方案未配置连续包月价格或价格配置错误");
        }
        BigDecimal renewPrice = relevance.getRenewPrice();
        log.info("计算还款卡首次开通续费金额返回：{}", renewPrice);
        return renewPrice;
    }

    @Override
    public BigDecimal calPlusProductOrderAmount(PlusProductOrderCreateEvent createEvent,
            BigDecimal discount, BigDecimal mallMobilePrice) {
        log.info("计算会员商品订单金额,入参:{},折扣:{},单价:{},商品数量:{}",
                JSON.toJSONString(createEvent), discount, mallMobilePrice,
                createEvent.getProductNum());
        if (createEvent.getProductNum() == null || createEvent.getProductNum() == 0) {
            log.info("计算会员商品订单金额,商品数量为0");
            throw new PlusAbyssException("商品数量不能为0");
        }
        if (discount == null) {
            log.info("计算会员商品订单,无折扣,原价返回");
            return mallMobilePrice;
        }
        BigDecimal bigDecimal = mallMobilePrice.multiply(discount)
                .multiply(BigDecimal.valueOf(createEvent.getProductNum()))
                .setScale(2, RoundingMode.DOWN);
        log.info("计算会员商品订单金额返回:{}", bigDecimal);
        return bigDecimal;
    }

    @Override
    public PlusDiscountEntity getNewDiscount(Integer userId, Integer channelId, Integer configId,
            DistributionMarketEntity market) {
        if (configId != JuziPlusEnum.EXPEDITE_CARD.getCode()) {
            log.info("借款首页非加速卡,不处理新折扣逻辑：{},{}", userId, configId);
            return null;
        }
        // 是否有生效中折扣
        String redisKey = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.MEMBER_DISCOUNT,
                userId, configId);
        String discountCache = redisUtils.get(redisKey);
        log.info("获取生效中新折扣缓存结果：{}", discountCache);
        // 有生效中折扣，直接返回
        if (org.apache.commons.lang3.StringUtils.isNotBlank(discountCache)) {
            log.info("新折扣生效中折扣缓存处理完成：{}", userId);
            MemberPlusDiscountRecordPo record = JSONObject.parseObject(discountCache,
                    MemberPlusDiscountRecordPo.class);
            return buildNewDiscount(record);
        }
        // 2023.12.19 huxf 调用桔策，判断加速卡是否打折，并且返回客群类型
        if (market == null || market.getJskDiscount().equals(2)) {
            log.info("新折扣桔策返回不打折扣:{}", userId);
            return null;
        }
        log.info("获取新会员折扣开始：{}，{}，{}", userId, configId, market.getConfTag());
        // 用户已使用过的折扣
        List<MemberPlusDiscountRecordPo> userRecordList = iDiscountRepository.getUserRecordList(
                userId, channelId, DiscountConfTypeEnum.PLUS.getCode(), configId);
        log.info("获取新会员折扣,用户已经使用过的折扣：{}", JSON.toJSONString(userRecordList));
        // 用户已经使用过的折扣code
        List<String> useConfCodes = userRecordList.stream()
                .map(MemberPlusDiscountRecordPo::getConfCode).distinct()
                .collect(Collectors.toList());
        log.info("获取新会员折扣,用户已经使用过的折扣code：{}", JSON.toJSONString(useConfCodes));
        // 未使用过的折扣条件
        // 2023.12.19 huxf 查询条件增加客群类型，普通客群查普通客群折扣，重提客群查重提客群折扣
        List<PlusDiscountConditionPo> conditionList = iDiscountRepository.selectByNotConfCode(
                useConfCodes, configId, market.getConfTag());
        log.info("获取新会员折扣,用户未使用的折扣条件：{}", JSON.toJSONString(conditionList));
        if (CollectionUtils.isEmpty(conditionList)) {
            log.info("获取新会员折扣,用户已使用全部折扣,无法打折：{}", userId);
            return null;
        }
        // 获取大数据指标
        List<UserSignInfoEntity> userBrowseVariable = userSignExternalRepository.getUserBrowseVariable(
                userId, DiscountConditionFieldEnum.getVariableCodes());
        if (userBrowseVariable == null) {
            log.info("获取新会员折扣,未获取到大数据指标,无法打折：{}", userId);
            return null;
        }
        // 匹配折扣条件。按折扣code分组
        Map<String, List<PlusDiscountConditionPo>> conditionMap = conditionList.stream()
                .collect(Collectors.groupingBy(PlusDiscountConditionPo::getConfCode));
        log.info("开始匹配折扣，按折扣分组后结果：{}", JSON.toJSONString(conditionMap));
        List<MemberPlusDiscountConfPo> matchDiscountList = new ArrayList<>();
        for (Entry<String, List<PlusDiscountConditionPo>> entry : conditionMap.entrySet()) {
            log.info("开始匹配,折扣code={}", entry.getKey());
            // 按字段分组
            Map<String, List<PlusDiscountConditionPo>> fieldCondtionMap = entry.getValue().stream()
                    .collect(Collectors.groupingBy(PlusDiscountConditionPo::getConditionField));
            // 当前折扣下的所有条件是否全部匹配通过：只要其中任何一个条件不符合,则当前折扣匹配失败
            boolean match = true;
            for (Entry<String, List<PlusDiscountConditionPo>> fieldEntry : fieldCondtionMap.entrySet()) {
                String fieldKey = fieldEntry.getKey();
                // 屯单时长（分钟）
                if (fieldKey.equals(DiscountConditionFieldEnum.STOCK_TIME.getCode())
                        && !matchCondition(fieldEntry, userBrowseVariable,
                        DiscountConditionFieldEnum.STOCK_TIME)) {
                    log.info("屯单时长未匹配通过，当前折扣跳过匹配：{}", entry.getKey());
                    match = false;
                    break;
                }
                // 当日浏览加速卡广告次数
                else if (fieldKey.equals(
                        DiscountConditionFieldEnum.TODAY_BROWSE_PLUS_ADVERT_TIME.getCode())
                        && !matchCondition(fieldEntry, userBrowseVariable,
                        DiscountConditionFieldEnum.TODAY_BROWSE_PLUS_ADVERT_TIME)) {
                    log.info("浏览加速卡广告次数未匹配通过，当前折扣跳过匹配：{}", entry.getKey());
                    match = false;
                    break;
                }
                // 一年内放款成功次数
                else if (fieldKey.equals(
                        DiscountConditionFieldEnum.ONE_YEAR_LOAN_SUCCESS_TIMES.getCode())
                        && !matchCondition(fieldEntry, userBrowseVariable,
                        DiscountConditionFieldEnum.ONE_YEAR_LOAN_SUCCESS_TIMES)) {
                    log.info("一年内放款成功次数未匹配通过，当前折扣跳过匹配：{}", entry.getKey());
                    match = false;
                    break;
                }
                // 一年内购买加速卡次数
                else if (fieldKey.equals(
                        DiscountConditionFieldEnum.ONE_YEAR_PAY_EXPEDITE_TIMES.getCode())
                        && !matchCondition(fieldEntry, userBrowseVariable,
                        DiscountConditionFieldEnum.ONE_YEAR_PAY_EXPEDITE_TIMES)) {
                    log.info("一年内购买加速卡次数未匹配通过，当前折扣跳过匹配：{}", entry.getKey());
                    match = false;
                    break;
                }
            }
            // 当前折扣下所有条件全部匹配通过
            if (match) {
                log.info("折扣条件全匹配通过,折扣code:{}", entry.getKey());
                MemberPlusDiscountConfPo vo = iDiscountRepository.getDiscountConfByCode(
                        entry.getKey());
                matchDiscountList.add(vo);
            }
        }
        log.info("匹配的条件列表：{}", JSON.toJSONString(matchDiscountList));
        if (!CollectionUtils.isEmpty(matchDiscountList)) {
            MemberPlusDiscountConfPo vo = matchDiscountList.stream()
                    .min(Comparator.comparing(MemberPlusDiscountConfPo::getId)).get();
            log.info("新会员折扣最终返回：{}", JSON.toJSONString(vo));
            MemberPlusDiscountRecordPo record = saveUserDiscountInfo(vo, userId, channelId);
            return buildNewDiscount(record);
        }
        log.info("未匹配到任何折扣：{}", userId);
        return null;
    }


    /**
     * 计算通用的会员费
     */
    private BigDecimal calGeneralPlusOrderAmount(PlusOrderCreateEvent event,
            PlusProgramEntity programEntity, BigDecimal discount) {
        Integer renew = event.getRenew();
        Integer userId = event.getUserId();
        if (renew != null && renew == 1) {
            //续费订单价格
            log.info("创单时取续费价格：userId：{}，programId：{}，renewPrice：{}", userId,
                    programEntity.getProgramId(), event.getRenewPrice());
            if (event.getRenewPrice() == null) {
                throw new PlusAbyssException("未获取到续费价格");
            }
            //续费价格不为空直接返回
            return event.getRenewPrice();
        } else {
            if (event.getCreateOrderContext() == null) {
                event.setCreateOrderContext(new CreateOrderContext());
            }
            // 20230508 zjf 联名卡虚拟权益价格处理
            boolean lmk = BusinessTypeEnum.LMK.getCode().equals(event.getBusinessType());
            log.info("处理会员创单金额业务标识：{},用户id：{}", event.getBusinessType(), userId);
            if (discount != null) {
                BigDecimal discountPrice = discount.multiply(programEntity.getMallMobilePrice());
                BigDecimal orderMoney = discountPrice.setScale(0, RoundingMode.DOWN);
                // 上下文赋值会员订单价格
                event.getCreateOrderContext().setPlusAmount(orderMoney);
                if (lmk) {
                    BigDecimal lmkAmount = getLmkAmount(event);
                    orderMoney = orderMoney.add(lmkAmount);
                    log.info("创单时用联名卡折扣价格：{},{},{}", orderMoney, lmkAmount, userId);
                    // 上下文赋值联名卡虚拟权益价格
                    event.getCreateOrderContext().setLmkAmount(lmkAmount);
                }
                log.info("创单时用折扣价格：userId：{}，programId：{}，renewPrice：{}", userId,
                        programEntity.getProgramId(), orderMoney);
                return orderMoney;
            }
            boolean flag = programEntity.getFirstOrderPrice() != null && isFirstBuy(userId,
                    programEntity.getProgramId());
            // 20230508 zjf 联名卡虚拟权益价格处理
            BigDecimal amount =
                    flag ? programEntity.getFirstOrderPrice() : programEntity.getMallMobilePrice();
            // 上下文赋值会员订单价格
            event.getCreateOrderContext().setPlusAmount(amount);
            if (lmk) {
                BigDecimal lmkAmount = getLmkAmount(event);
                amount = amount.add(lmkAmount);
                log.info("创单时用联名卡非折扣价格：{},{}", amount, userId);
                // 上线复制会员价格
                event.getCreateOrderContext().setLmkAmount(lmkAmount);
            }
            return amount;
        }
    }

    /**
     * 该用户是否第一次购买本方案 true是
     *
     * <AUTHOR>
     * @date 2020/2/8 5:13 PM
     */
    private boolean isFirstBuy(Integer memberId, Integer programId) {
        //方案为设置首单价，返回false
        PlusProgramEntity program = programQueryModel.getById(programId);
        if (StringUtils.isEmpty(program.getFirstOrderPrice())) {
            return false;
        }
        if (memberId == null || memberId == 0) {
            return true;
        }
        //根据用户id 与方案id 查用户是否享受过优惠价，如果购买过就返回定价
        // 20230322 zjf 替换为plus_order_info
        return orderRepository.getNumByUserAndProId(memberId, programId) == 0;
    }

    /**
     * 获取联名卡虚拟权益售价
     */
    private BigDecimal getLmkAmount(PlusOrderCreateEvent event) {
        PlusProgramLmkVirtualEntity virtual = programQueryModel.getVirtualById(
                event.getCreateOrderContext().getVirtualId());
        if (virtual == null) {
            throw new PlusAbyssException("无效的联名卡id");
        }
        Integer productId = virtual.getProductId();
        log.info("获取联名卡虚拟权益商品售价：{}", productId);
        Product product = productExternalRepository.getProductById(productId);
        if (product == null) {
            throw new PlusAbyssException("联名卡商品id无效，获取商品信息为空");
        }
        log.info("获取联名卡虚拟权益商品售价返回：{}", product.getMallMobilePrice());
        return product.getMallMobilePrice();
    }

    /**
     * 触发逻辑处理折扣信息
     */
    private PlusDiscountEntity doHandleDiscountInfo(Integer userId, Integer configId,
            Integer channel, String redisKey, String cacheInfo) {
        String confCode;
        // 20230506 zjf 桔会卡走桔策
        if (JuziPlusEnum.JH_CARD.getCode() == configId) {
            log.info("触发逻辑处理折扣信息，桔会卡：{},{}", userId, configId);
            JSONArray result = engineExternalRepository.enginePreprocess(null, userId,
                    "YQshopVIPzhekou", channel);
            if (CollectionUtils.isEmpty(result)) {
                log.info("触发逻辑处理折扣信息，桔会卡获取桔策结果为空：{}", userId);
                return null;
            }
            JSONArray channelResult = result.getJSONObject(0).getJSONArray("channelResult");
            if (CollectionUtils.isEmpty(channelResult)) {
                log.info("触发逻辑处理折扣信息，桔会卡获取渠道结果集为空：{}", userId);
                return null;
            }
            confCode = channelResult.getString(0);
        } else {
            log.info("触发逻辑处理折扣信息，非桔会卡：{},{}", userId, configId);
            //查询用户标签信息
            UserSignInfoEntity signInfo = userSignExternalRepository.getUserSignByConfigId(userId,
                    channel, configId);
            log.info("判断折扣条件-标签信息为,userId:{}，result：{}", userId,
                    JSONObject.toJSONString(signInfo));
            if (signInfo == null || StringUtils.isEmpty(signInfo.getVariableValue())) {
                log.info("校验用户是否折扣-标签获取失败,userId:{},result:{}", userId,
                        JSON.toJSONString(signInfo));
                return null;
            }
            confCode = signInfo.getVariableValue();
        }
        //通过code查询member_plus_discount_conf表 校验状态
        MemberPlusDiscountConfPo confVo = iDiscountRepository.getDiscountConfByCode(confCode);
        if (confVo == null || !confVo.getConfigId().equals(configId)) {
            log.info("校验用户是否折扣-配置信息有误,userId:{},confVo:{}", userId, confVo);
            return null;
        }
        if (cacheInfo != null) {
            //缓存中是符合状态，需要校验：标签 + conf状态,通过返回，缓存中的数据
            return JSONObject.parseObject(cacheInfo, PlusDiscountEntity.class);
        }
        //查询member_plus_discount_record
        MemberPlusDiscountRecordPo userRecord = iDiscountRepository.getUserRecord(userId, channel,
                confCode);
        log.info("查询用户参与折扣记录,userId:{},result:{}", userId, JSON.toJSONString(userRecord));
        if (userRecord != null) {
            //已经在当前code下营销过期
            log.info("校验用户是否折扣-已经在当前code下营销过,userId:{}", userId);
            //是否符合-缓存-不符合
            redisUtils.setEx(redisKey, RedisConstantPrefix.ZERO, 2, TimeUnit.MINUTES);
            return null;
        }
        //封装折扣信息
        PlusDiscountEntity discountVo = buildResultVo(confVo);
        log.info("校验用户是否折扣返回,userId:{},result:{}", userId, JSON.toJSONString(discountVo));
        //记录营销记录
        saveDiscountRecord(userId, channel, confVo, discountVo,
                DiscountConfTypeEnum.CUSTOMER_GROUP);
        //放入缓存
        redisUtils.setEx(redisKey, JSONObject.toJSONString(discountVo), confVo.getEffectiveTime(),
                TimeUnit.MINUTES);
        return discountVo;
    }

    /**
     * 构建返回信息
     */
    private PlusDiscountEntity buildResultVo(MemberPlusDiscountConfPo confVo) {
        PlusDiscountEntity discountVo = new PlusDiscountEntity();
        discountVo.setDiscountRate(confVo.getDiscountRate());
        discountVo.setShowDiscountInfo(1);
        Integer effectiveTime = confVo.getEffectiveTime();
        //计算截止日期
        Date dateEndTime = DateUtils.addMinutes(new Date(), effectiveTime);
        discountVo.setDiscountEndTime(dateEndTime);
        return discountVo;
    }

    /**
     * 构建新折扣返回信息
     */
    private PlusDiscountEntity buildNewDiscount(MemberPlusDiscountRecordPo record) {
        PlusDiscountEntity entity = new PlusDiscountEntity();
        entity.setDiscountRate(record.getDiscountRate());
        entity.setShowDiscountInfo(1);
        Integer effectiveTime = record.getEffectiveTime();
        //计算截止日期
        Date dateEndTime = DateUtils.addMinutes(new Date(), effectiveTime);
        entity.setDiscountEndTime(dateEndTime);
        return entity;
    }

    /**
     * 保存折扣纪录
     */
    private MemberPlusDiscountRecordPo saveDiscountRecord(Integer userId, Integer channel,
            MemberPlusDiscountConfPo confVo, PlusDiscountEntity discountVo,
            DiscountConfTypeEnum confType) {
        MemberPlusDiscountRecordPo targetRecord = new MemberPlusDiscountRecordPo();
        targetRecord.setUserId(userId);
        targetRecord.setChannelId(channel);
        targetRecord.setConfCode(confVo.getConfCode());
        targetRecord.setConfigId(confVo.getConfigId());
        targetRecord.setEffectiveTime(confVo.getEffectiveTime());
        targetRecord.setDiscountStartTime(new Date());
        targetRecord.setDiscountEndTime(discountVo.getDiscountEndTime());
        // 20231023 zjf 新增折扣类型、折扣
        targetRecord.setConfType(confType.getCode());
        targetRecord.setDiscountRate(confVo.getDiscountRate());
        // 20231220 huxf 新增客群标签,只用于快照记录
        targetRecord.setConfTag(confVo.getConfTag());
        iDiscountRepository.saveDiscountRecord(targetRecord);
        log.info("保存用户折扣营销记录成功：{}，{}", userId, confType);
        return targetRecord;
    }

    /**
     * 匹配条件
     */
    private boolean matchCondition(Entry<String, List<PlusDiscountConditionPo>> fieldEntry,
            List<UserSignInfoEntity> userBrowseVariable, DiscountConditionFieldEnum field) {
        String fieldName = field.getName();
        // 获取大数据指标值
        String variableValue = getUserBrowseVariableValue(userBrowseVariable,
                field.getVariableCode());
        if (org.apache.commons.lang3.StringUtils.isBlank(variableValue)) {
            variableValue = "0";
            log.info("大数据未返回[{}]指标值,默认为0：{}", fieldName, field.getCode());
        }
        List<PlusDiscountConditionPo> value = fieldEntry.getValue();
        // 从左至右逐步匹配
        for (int i = 0; i < value.size(); i++) {
            PlusDiscountConditionPo condition = value.get(i);
            String name = i == 0 ? "左边" : "右边";
            boolean matchResult = matchSingleCondition(fieldName, name, variableValue,
                    condition.getConditionKey(), condition.getConditionVal());
            if (!matchResult) {
                log.info("[{}][{}]条件未匹配通过：{},{}", fieldName, name,
                        condition.getConditionKey(), condition.getConditionVal());
                return false;
            }
        }
        log.info("[{}]条件全匹配通过", fieldName);
        return true;
    }


    /**
     * 获取某个指标值
     */
    private String getUserBrowseVariableValue(List<UserSignInfoEntity> userBrowseVariable,
            String code) {
        return userBrowseVariable.stream().filter(e -> e.getVariableCode().equals(code)).findFirst()
                .map(UserSignInfoEntity::getVariableValue).orElse(null);
    }

    /**
     * 匹配单个条件
     *
     * @param fieldName 字段
     * @param name 左边/右边
     * @param conditionKey 条件
     * @param conditionVal 条件值
     * @param variableValue 大数据变量值
     * @return 是否匹配当前条件
     */
    private boolean matchSingleCondition(String fieldName, String name, String variableValue,
            String conditionKey, String conditionVal) {
        // 未配置的条件，当做匹配通过
        if (org.apache.commons.lang3.StringUtils.isBlank(conditionVal)) {
            log.info("[{}][{}]未配置条件，不做条件匹配", fieldName, name);
            return true;
        }
        // 获取条件枚举
        DiscountConditionKeyEnum keyEnum = DiscountConditionKeyEnum.getByCode(conditionKey);
        if (keyEnum == null) {
            throw new PlusAbyssException("无效的折扣条件:" + conditionKey);
        }
        log.info("[{}][{}][{}]条件开始匹配,条件值={},源对比值{}", fieldName, name,
                keyEnum.getName(), conditionVal, variableValue);
        boolean conditionMatch = false;
        // 源对比值，大数据返回
        double sourceValue = Double.parseDouble(variableValue);
        // 条件值，后台折扣配置
        double targetValue = Double.parseDouble(conditionVal);
        switch (keyEnum) {
            case EQ:
                conditionMatch = sourceValue == targetValue;
                break;
            case GT:
                conditionMatch = sourceValue > targetValue;
                break;
            case GTE:
                conditionMatch = sourceValue >= targetValue;
                break;
            case LT:
                conditionMatch = sourceValue < targetValue;
                break;
            case LTE:
                conditionMatch = sourceValue <= targetValue;
                break;
            default:
                log.info("未匹配到条件枚举,按匹配不通过处理：{}", conditionKey);
                break;
        }
        log.info("[{}][{}][{}]条件匹配结果={}", fieldName, name, keyEnum.getName(), conditionMatch);
        return conditionMatch;
    }

    /**
     * 保存用户折扣信息
     */
    private MemberPlusDiscountRecordPo saveUserDiscountInfo(MemberPlusDiscountConfPo vo,
            Integer userId, Integer channel) {
        // 保存折扣营销记录
        PlusDiscountEntity discountVo = buildResultVo(vo);
        MemberPlusDiscountRecordPo record = saveDiscountRecord(userId, channel, vo, discountVo,
                DiscountConfTypeEnum.PLUS);
        // 折扣生效
        String redisKey = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.MEMBER_DISCOUNT,
                userId, vo.getConfigId());
        redisUtils.setEx(redisKey, JSONObject.toJSONString(record), vo.getEffectiveTime(),
                TimeUnit.MINUTES);
        log.info("生效折扣成功：{}", redisKey);
        return record;
    }
}
