# 分期支付分账逻辑重构说明

## 重构目标

将 `PlusOrderApplicationImpl` 类中的分期支付分账相关方法重构到 `PlusOrderSeparateModel` 中，提高代码的内聚性和可维护性。

## 重构内容

### 1. 接口扩展

#### 1.1 PlusOrderSeparateModel 接口新增方法
**文件**：`PlusOrderSeparateModel.java`

新增方法声明：
```java
/**
 * 获取第二期支付分账信息
 */
PlusOrderDeductResEntity getSecondPeriodSeparateInfo(PlusOrderEntity plusOrderEntity, 
        PlusDeductEvent deductPlan);

/**
 * 创建第二期支付分账信息
 */
PlusOrderDeductResEntity createSecondPeriodSeparateInfo(PlusOrderEntity plusOrderEntity,
        PlusDeductEvent deductPlan, PlusOrderSeparateEntity firstPeriodTemplate, 
        List<PlusOrderSeparateItemEntity> firstPeriodItems, BigDecimal secondPayAmount);

/**
 * 创建第二期支付的分账明细项
 */
List<PlusOrderSeparateItemEntity> createSecondPeriodSeparateItems(
        List<PlusOrderSeparateItemEntity> firstPeriodItems, 
        PlusOrderSeparateEntity secondPeriodSeparate, 
        BigDecimal secondPayAmount);
```

新增 import：
```java
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemEntity;
import com.juzifenqi.plus.enums.OrderPayActionEnum;
import java.math.BigDecimal;
```

### 2. 实现类扩展

#### 2.1 PlusOrderSeparateModelImpl 实现类新增方法
**文件**：`PlusOrderSeparateModelImpl.java`

新增 import：
```java
import com.juzifenqi.plus.enums.supplier.SeparateTypeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
```

#### 2.2 核心方法实现

**getSecondPeriodSeparateInfo 方法**：
- 查询第一期支付的分账记录
- 查询第一期支付的清分主体明细记录
- 计算第二期支付金额
- 调用 `createSecondPeriodSeparateInfo` 创建第二期分账信息

**createSecondPeriodSeparateInfo 方法**：
- 创建第二期分账主记录
- 复用第一期的分流主体信息
- 调用 `createSecondPeriodSeparateItems` 创建分账明细
- 构建并返回结果

**createSecondPeriodSeparateItems 方法**：
- 遍历第一期的分账明细项
- 复用第一期的配置信息（supplierType、merchantId、separateType、separateRate）
- 根据分账方式重新计算分账金额
- 返回第二期的分账明细列表

### 3. 调用方修改

#### 3.1 PlusOrderApplicationImpl 修改
**文件**：`PlusOrderApplicationImpl.java`

**删除的方法**：
- `getSecondPeriodSeparateInfo()`
- `createSecondPeriodSeparateInfo()`
- `createSecondPeriodSeparateItems()`
- `getApplySerialNo()` (重复方法)

**修改的调用**：
```java
// 原来的调用
return getSecondPeriodSeparateInfo(plusOrderEntity, deductPlan);

// 修改后的调用
return separateModel.getSecondPeriodSeparateInfo(plusOrderEntity, deductPlan);
```

**删除的 import**：
```java
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemEntity;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderSeparateRepository;
```

**删除的依赖注入**：
```java
@Autowired
private IPlusOrderSeparateRepository separateRepository;
```

### 4. 方法签名适配

#### 4.1 getApplySerialNo 方法复用
由于 `PlusOrderSeparateModelImpl` 中已经存在 `getApplySerialNo` 方法，但签名不同：
- 现有方法：`getApplySerialNo(OrderPayActionEnum payAction, String orderSn)`
- 新方法需求：`getApplySerialNo(Integer payActionCode, String orderSn)`

**解决方案**：复用现有方法，修改调用方式：
```java
// 修改前
secondPeriodSeparate.setApplySerialNo(getApplySerialNo(OrderPayActionEnum.PAY_ACTION_4.getCode(), plusOrderEntity.getOrderSn()));

// 修改后
secondPeriodSeparate.setApplySerialNo(getApplySerialNo(OrderPayActionEnum.PAY_ACTION_4, plusOrderEntity.getOrderSn()));
```

## 重构效果

### 1. 代码组织优化
- **职责分离**：分账相关逻辑集中到 `PlusOrderSeparateModel` 中
- **内聚性提升**：相关的业务逻辑聚合在同一个模块
- **可维护性增强**：分账逻辑的修改只需要在一个地方进行

### 2. 架构层次清晰
- **Application 层**：专注于业务流程编排和接口适配
- **Model 层**：专注于核心业务逻辑实现
- **Repository 层**：专注于数据访问

### 3. 代码复用性
- 分账相关的方法可以被其他模块复用
- 避免了代码重复和逻辑分散

## 重构验证要点

### 1. 功能验证
- 第一期支付的分账信息生成是否正常
- 第二期支付的分账信息生成是否正常
- 分流清分信息的复用是否正确

### 2. 接口兼容性
- 现有的调用方是否正常工作
- 方法签名是否匹配
- 返回值类型是否一致

### 3. 依赖关系
- import 语句是否正确
- 依赖注入是否完整
- 循环依赖是否存在

## 总结

本次重构成功将分期支付的分账逻辑从 Application 层迁移到 Model 层，实现了：

1. **职责分离**：Application 层专注于流程编排，Model 层专注于业务逻辑
2. **代码内聚**：相关的分账逻辑集中管理
3. **可维护性**：分账逻辑的修改和扩展更加便利
4. **架构清晰**：符合 DDD 分层架构的设计原则

重构后的代码结构更加清晰，便于后续的功能扩展和维护。
