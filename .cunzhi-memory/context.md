# 项目上下文信息

- plus-abyss项目：基于Spring Boot 2.6.8的Java微服务项目，采用DDD架构设计，是桔子分期的会员营销系统。包含5个模块：domain(核心业务)、api(接口定义)、config(配置)、external(外部集成)、job(定时任务)。核心业务模块包括：market(营销)、order(订单)、asserts(会员权益)、program(方案)、settle(结算)。营销流程涉及多个场景：支付完成页、借款首页、确认借款页等。通过MagicExternal调用外部营销服务API。
- 用户需要实现会员分期支付功能，支持2期分期：首期用户主动支付，二期支持主动支付或借款单自动划扣。需要添加first_pay_amount字段到plus_order_info表，新增PAY_FIRST_PERIOD枚举，实现首付金额计算和订单服务调用逻辑。
- 用户需要在现有分期支付基础上实现收银台分期支付功能：第一期使用firstPayAmount进行支付和分账，并保存分流清分信息；第二期复用分流清分信息，使用剩余金额进行支付。需要修改支付信息生成逻辑和分账处理逻辑。
