-- 会员分期支付功能数据库表结构变更脚本
-- 执行时间：请在测试环境验证后再在生产环境执行

-- 1. 为 plus_order_info 表添加 first_pay_amount 字段
ALTER TABLE `plus_order_info` 
ADD COLUMN `first_pay_amount` DECIMAL(10,2) NULL COMMENT '首付金额（分期支付场景使用）' 
AFTER `pay_type`;

-- 2. 更新支付方式字段注释，增加分期支付说明
ALTER TABLE `plus_order_info` 
MODIFY COLUMN `pay_type` INT(11) NULL COMMENT '支付方式 1-全款支付2-划扣3-后付款4-全款划扣5-首期支付';

-- 3. 验证字段添加是否成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = 'your_database_name' 
-- AND TABLE_NAME = 'plus_order_info' 
-- AND COLUMN_NAME = 'first_pay_amount';

-- 4. 查看表结构确认
-- DESC plus_order_info;
