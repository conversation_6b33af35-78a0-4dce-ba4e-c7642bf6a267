package com.juzifenqi.plus.dto.resp.admin;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Data;


/**
 * 会员订单详情返回对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/2 10:38
 */
@Data
public class PlusOrderDetailResp implements Serializable {
    private static final long serialVersionUID = 42L;

    /**
     * ===========================订单信息=======================================
     */
    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 实付金额
     */
    private BigDecimal payAmount;

    /**
     * 支付状态 0=待支付 1=支付中 2=支付成功 3=支付失败 4=退款 5=部分还款成功 6=未知
     */
    private Integer payState;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 会员类别id
     */
    private Integer configId;

    /**
     * 会员状态  1生效  0过期  2失效 -1未知（前端展示空）,
     */
    private Integer plusState;

    /**
     * 会员生效时间
     */
    private Integer effectiveNum;

    /**
     * 当前会员开始时间
     */
    private String currentStartTime;

    /**
     * 当前会员结束时间
     */
    private String currentEndTime;

    /**
     * 会员支付方式(中文)
     * 1-全款支付   3-后付款  5-首付支付
     */
    private String payType;

    /**
     * 首付金额
     */
    private BigDecimal firstPayAmount;


    /**
     * 订单状态
     */
    private Integer orderState;

    /**
     * 会员支付卡号
     */
    private String payCardNo;

    /**
     * 会员支付方式 code
     */
    private String payTypeCode;

    /**
     * 发送的提额信息
     */
    private String quotaInfo;

    /**
     * 是否发送提额信息
     */
    private Boolean sendQuotaInfo;

    /**
     * ===========================退款信息=======================================
     */
    /**
     * 是否可退会员
     */
    private Boolean canBeCancel;

    /**
     * 退款状态: 0_待处理 1_退款中 2_退款成功 3_退款失败 4_存疑
     */
    private Integer refundState;

    /**
     * 退款比例
     */
    private BigDecimal refundRatio;

    /**
     * 退款银行信息（银行卡后四位）
     */
    private String refundBankCardNo;

    /**
     * 打款银行名称
     */
    private String refundBankName;

    /**
     * 退款失败原因
     */
    private String refundFailMsg;

    /**
     * 退款方式：0 原路退 1 代付
     */
    private Integer refundType;

    /**
     * 出账方(实际出账方)
     */
    private String outSupplier;

    /**
     * 计划入账方
     */
    private String planInSupplier;

    /**
     * 实际入账方
     */
    private String inSupplier;

    /**
     * 退款流水号
     */
    private String refundSerialNumber;

    /**
     * 是否扣减生活权益差价
     */
    private Boolean needDeductPrice;

    /**
     * 权益数量
     */
    private Integer profitNum;

    /**
     * 差价
     */
    private BigDecimal deductPrice;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款通道
     */
    private String refundChannel;
    /**
     * ===========================用户信息=======================================
     */
    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 会员detail 表主键id
     */
    private Integer detailId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员总生命周期开始时间
     */
    private String startTime;

    /**
     * 会员总生命周期结束时间
     */
    private String endTime;

    /**
     * 全部会员卡类型：（桔享，固额，加速）
     */
    private String allPlusType;

    /**
     * ===========================权益信息=======================================
     */
    private Map<Integer, Object> profitList;

    /**
     * ===========================融担咨询卡关联信息================================
     */

    /**
     * 借款单号
     */
    private String loanOrderSn;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 计划划扣日
     */
    private String deductionTime;

    /**
     * 预期天数
     */
    private String overdueDays;

    /**
     * 支付记录
     */
    private List<PlusOrderSeparateResp> payRecordList;


}
