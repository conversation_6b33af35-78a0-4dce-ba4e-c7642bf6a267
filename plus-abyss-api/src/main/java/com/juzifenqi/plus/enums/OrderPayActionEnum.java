package com.juzifenqi.plus.enums;

/**
 * 订单支付行为
 */
public enum OrderPayActionEnum {

    PAY_ACTION_1(1, "主动支付"),
    PAY_ACTION_2(2, "系统划扣"),
    PAY_ACTION_3(3, "分期首付"),
    PAY_ACTION_4(4, "分期二付"),
    ;

    private int code;

    private String name;

    OrderPayActionEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
